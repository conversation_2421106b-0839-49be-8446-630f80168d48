"use strict";
/**
 * Contact Settings Controller
 *
 * Production-grade controller for managing contact information and social media links
 * with proper validation, security, and error handling.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateBulkUpdate = exports.validateContactSetting = exports.bulkUpdateContactSettings = exports.deleteContactSetting = exports.updateContactSetting = exports.createContactSetting = exports.getContactSettingsByCategory = exports.getPublicContactSettings = exports.getContactSettingsGrouped = exports.getAllContactSettings = void 0;
const express_validator_1 = require("express-validator");
const ContactSettings_1 = require("../models/ContactSettings");
const apiResponse_1 = require("../utils/apiResponse");
/**
 * Get all contact settings (Admin only)
 */
const getAllContactSettings = async (req, res) => {
    try {
        const settings = await ContactSettings_1.ContactSettingsModel.getAll();
        return (0, apiResponse_1.sendSuccess)(res, settings, 'Contact settings retrieved successfully');
    }
    catch (error) {
        console.error('Get all contact settings error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to retrieve contact settings', error);
    }
};
exports.getAllContactSettings = getAllContactSettings;
/**
 * Get contact settings grouped by category (Admin only)
 */
const getContactSettingsGrouped = async (req, res) => {
    try {
        const settings = await ContactSettings_1.ContactSettingsModel.getAllGrouped();
        return (0, apiResponse_1.sendSuccess)(res, settings, 'Contact settings retrieved successfully');
    }
    catch (error) {
        console.error('Get grouped contact settings error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to retrieve contact settings', error);
    }
};
exports.getContactSettingsGrouped = getContactSettingsGrouped;
/**
 * Get public contact settings (Public endpoint)
 */
const getPublicContactSettings = async (req, res) => {
    try {
        const settings = await ContactSettings_1.ContactSettingsModel.getPublicSettings();
        // Set cache headers for better performance
        res.setHeader('Cache-Control', 'public, max-age=3600'); // 1 hour cache
        return (0, apiResponse_1.sendSuccess)(res, settings, 'Public contact settings retrieved successfully');
    }
    catch (error) {
        console.error('Get public contact settings error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to retrieve contact settings', error);
    }
};
exports.getPublicContactSettings = getPublicContactSettings;
/**
 * Get contact settings by category (Admin only)
 */
const getContactSettingsByCategory = async (req, res) => {
    try {
        const { category } = req.params;
        if (!['general', 'social', 'contact', 'address'].includes(category)) {
            return (0, apiResponse_1.sendError)(res, 'Invalid category', null, 400);
        }
        const settings = await ContactSettings_1.ContactSettingsModel.getByCategory(category);
        return (0, apiResponse_1.sendSuccess)(res, settings, `${category} contact settings retrieved successfully`);
    }
    catch (error) {
        console.error('Get contact settings by category error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to retrieve contact settings', error);
    }
};
exports.getContactSettingsByCategory = getContactSettingsByCategory;
/**
 * Create a new contact setting (Admin only)
 */
const createContactSetting = async (req, res) => {
    var _a;
    try {
        // Validate input
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return (0, apiResponse_1.sendValidationError)(res, errors.array());
        }
        const { settingKey, settingValue, settingType, category, displayOrder, isActive } = req.body;
        const adminId = (_a = req.admin) === null || _a === void 0 ? void 0 : _a.id;
        // Check if setting key already exists
        const existingSetting = await ContactSettings_1.ContactSettingsModel.getByKey(settingKey);
        if (existingSetting) {
            return (0, apiResponse_1.sendError)(res, 'Setting key already exists', null, 409);
        }
        const newSetting = await ContactSettings_1.ContactSettingsModel.create({
            settingKey,
            settingValue,
            settingType,
            category,
            displayOrder,
            isActive,
            createdByAdmin: adminId
        });
        return (0, apiResponse_1.sendSuccess)(res, newSetting, 'Contact setting created successfully', 201);
    }
    catch (error) {
        console.error('Create contact setting error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to create contact setting', error);
    }
};
exports.createContactSetting = createContactSetting;
/**
 * Update a contact setting (Admin only)
 */
const updateContactSetting = async (req, res) => {
    var _a;
    try {
        // Validate input
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return (0, apiResponse_1.sendValidationError)(res, errors.array());
        }
        const { id } = req.params;
        const { settingValue, settingType, category, displayOrder, isActive } = req.body;
        const adminId = (_a = req.admin) === null || _a === void 0 ? void 0 : _a.id;
        const updatedSetting = await ContactSettings_1.ContactSettingsModel.update(parseInt(id), {
            settingValue,
            settingType,
            category,
            displayOrder,
            isActive,
            updatedByAdmin: adminId
        });
        if (!updatedSetting) {
            return (0, apiResponse_1.sendError)(res, 'Contact setting not found', null, 404);
        }
        return (0, apiResponse_1.sendSuccess)(res, updatedSetting, 'Contact setting updated successfully');
    }
    catch (error) {
        console.error('Update contact setting error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to update contact setting', error);
    }
};
exports.updateContactSetting = updateContactSetting;
/**
 * Delete a contact setting (Admin only)
 */
const deleteContactSetting = async (req, res) => {
    var _a;
    try {
        const { id } = req.params;
        const adminId = (_a = req.admin) === null || _a === void 0 ? void 0 : _a.id;
        const deleted = await ContactSettings_1.ContactSettingsModel.delete(parseInt(id), adminId);
        if (!deleted) {
            return (0, apiResponse_1.sendError)(res, 'Contact setting not found', null, 404);
        }
        return (0, apiResponse_1.sendSuccess)(res, null, 'Contact setting deleted successfully');
    }
    catch (error) {
        console.error('Delete contact setting error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to delete contact setting', error);
    }
};
exports.deleteContactSetting = deleteContactSetting;
/**
 * Bulk update contact settings (Admin only)
 */
const bulkUpdateContactSettings = async (req, res) => {
    var _a;
    try {
        // Validate input
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return (0, apiResponse_1.sendValidationError)(res, errors.array());
        }
        const { updates } = req.body;
        const adminId = (_a = req.admin) === null || _a === void 0 ? void 0 : _a.id;
        if (!Array.isArray(updates) || updates.length === 0) {
            return (0, apiResponse_1.sendError)(res, 'Updates array is required and cannot be empty', null, 400);
        }
        const updatedSettings = await ContactSettings_1.ContactSettingsModel.bulkUpdate(updates, adminId);
        return (0, apiResponse_1.sendSuccess)(res, updatedSettings, 'Contact settings updated successfully');
    }
    catch (error) {
        console.error('Bulk update contact settings error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to update contact settings', error);
    }
};
exports.bulkUpdateContactSettings = bulkUpdateContactSettings;
/**
 * Validation rules for contact settings
 */
exports.validateContactSetting = [
    (0, express_validator_1.body)('settingKey')
        .optional()
        .isLength({ min: 1, max: 100 })
        .withMessage('Setting key must be between 1 and 100 characters')
        .matches(/^[a-z_]+$/)
        .withMessage('Setting key must contain only lowercase letters and underscores'),
    (0, express_validator_1.body)('settingValue')
        .notEmpty()
        .withMessage('Setting value is required')
        .isLength({ max: 1000 })
        .withMessage('Setting value cannot exceed 1000 characters'),
    (0, express_validator_1.body)('settingType')
        .optional()
        .isIn(['text', 'email', 'phone', 'url', 'address'])
        .withMessage('Setting type must be one of: text, email, phone, url, address'),
    (0, express_validator_1.body)('category')
        .optional()
        .isIn(['general', 'social', 'contact', 'address'])
        .withMessage('Category must be one of: general, social, contact, address'),
    (0, express_validator_1.body)('displayOrder')
        .optional()
        .isInt({ min: 0 })
        .withMessage('Display order must be a non-negative integer'),
    (0, express_validator_1.body)('isActive')
        .optional()
        .isBoolean()
        .withMessage('isActive must be a boolean')
];
/**
 * Validation rules for bulk update
 */
exports.validateBulkUpdate = [
    (0, express_validator_1.body)('updates')
        .isArray({ min: 1 })
        .withMessage('Updates must be a non-empty array'),
    (0, express_validator_1.body)('updates.*.id')
        .isInt({ min: 1 })
        .withMessage('Each update must have a valid ID'),
    (0, express_validator_1.body)('updates.*.settingValue')
        .notEmpty()
        .withMessage('Each update must have a setting value')
        .isLength({ max: 1000 })
        .withMessage('Setting value cannot exceed 1000 characters')
];
