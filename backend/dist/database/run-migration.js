"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const database_1 = require("../config/database");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
async function runMigration() {
    try {
        console.log('Initializing database connection...');
        await (0, database_1.initializeDatabase)();
        console.log('Running French sections migration...');
        const migrationPath = path_1.default.join(__dirname, 'migrations', '005_add_french_sections_to_scholarships.sql');
        const migrationSQL = fs_1.default.readFileSync(migrationPath, 'utf8');
        // Execute the entire migration as one statement
        console.log('Executing migration...');
        await (0, database_1.query)(migrationSQL);
        console.log('Migration completed successfully!');
        process.exit(0);
    }
    catch (error) {
        console.error('Migration failed:', error);
        process.exit(1);
    }
}
runMigration();
