"use strict";
/**
 * Contact Settings Routes
 *
 * Production-grade routes for managing contact information and social media links
 * with proper authentication, validation, and rate limiting.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const contactSettings_controller_1 = require("../controllers/contactSettings.controller");
const auth_new_1 = require("../middleware/auth.new");
const router = express_1.default.Router();
// Public routes (no authentication required)
/**
 * GET /api/contact-settings/public
 * Get public contact settings for frontend display
 */
router.get('/public', contactSettings_controller_1.getPublicContactSettings);
// Admin routes (authentication required)
/**
 * GET /api/contact-settings
 * Get all contact settings (admin only)
 */
router.get('/', auth_new_1.requireAdmin, contactSettings_controller_1.getAllContactSettings);
/**
 * GET /api/contact-settings/grouped
 * Get contact settings grouped by category (admin only)
 */
router.get('/grouped', auth_new_1.requireAdmin, contactSettings_controller_1.getContactSettingsGrouped);
/**
 * GET /api/contact-settings/category/:category
 * Get contact settings by category (admin only)
 */
router.get('/category/:category', auth_new_1.requireAdmin, contactSettings_controller_1.getContactSettingsByCategory);
/**
 * POST /api/contact-settings
 * Create a new contact setting (admin only)
 */
router.post('/', auth_new_1.requireAdmin, contactSettings_controller_1.validateContactSetting, contactSettings_controller_1.createContactSetting);
/**
 * PUT /api/contact-settings/:id
 * Update a contact setting (admin only)
 */
router.put('/:id', auth_new_1.requireAdmin, contactSettings_controller_1.validateContactSetting, contactSettings_controller_1.updateContactSetting);
/**
 * DELETE /api/contact-settings/:id
 * Delete a contact setting (admin only)
 */
router.delete('/:id', auth_new_1.requireAdmin, contactSettings_controller_1.deleteContactSetting);
/**
 * PUT /api/contact-settings/bulk
 * Bulk update contact settings (admin only)
 */
router.put('/bulk', auth_new_1.requireAdmin, contactSettings_controller_1.validateBulkUpdate, contactSettings_controller_1.bulkUpdateContactSettings);
exports.default = router;
