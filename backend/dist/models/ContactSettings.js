"use strict";
/**
 * Contact Settings Model
 *
 * Production-grade model for managing contact information and social media links
 * with proper validation, caching, and security features.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactSettingsModel = void 0;
const database_1 = require("../config/database");
class ContactSettingsModel {
    /**
     * Get all contact settings grouped by category
     */
    static async getAllGrouped() {
        const result = await (0, database_1.query)(`
      SELECT 
        id,
        setting_key as "settingKey",
        setting_value as "settingValue",
        setting_type as "settingType",
        category,
        display_order as "displayOrder",
        is_active as "isActive",
        created_at as "createdAt",
        updated_at as "updatedAt",
        created_by_admin as "createdByAdmin",
        updated_by_admin as "updatedByAdmin"
      FROM contact_settings 
      WHERE is_active = true 
      ORDER BY category, display_order ASC
    `);
        const settings = result.rows;
        return {
            general: settings.filter(s => s.category === 'general'),
            social: settings.filter(s => s.category === 'social'),
            contact: settings.filter(s => s.category === 'contact'),
            address: settings.filter(s => s.category === 'address')
        };
    }
    /**
     * Get all contact settings as a flat array
     */
    static async getAll() {
        const result = await (0, database_1.query)(`
      SELECT 
        id,
        setting_key as "settingKey",
        setting_value as "settingValue",
        setting_type as "settingType",
        category,
        display_order as "displayOrder",
        is_active as "isActive",
        created_at as "createdAt",
        updated_at as "updatedAt",
        created_by_admin as "createdByAdmin",
        updated_by_admin as "updatedByAdmin"
      FROM contact_settings 
      ORDER BY category, display_order ASC
    `);
        return result.rows;
    }
    /**
     * Get contact settings by category
     */
    static async getByCategory(category) {
        const result = await (0, database_1.query)(`
      SELECT 
        id,
        setting_key as "settingKey",
        setting_value as "settingValue",
        setting_type as "settingType",
        category,
        display_order as "displayOrder",
        is_active as "isActive",
        created_at as "createdAt",
        updated_at as "updatedAt",
        created_by_admin as "createdByAdmin",
        updated_by_admin as "updatedByAdmin"
      FROM contact_settings 
      WHERE category = $1 AND is_active = true
      ORDER BY display_order ASC
    `, [category]);
        return result.rows;
    }
    /**
     * Get a specific contact setting by key
     */
    static async getByKey(settingKey) {
        const result = await (0, database_1.query)(`
      SELECT 
        id,
        setting_key as "settingKey",
        setting_value as "settingValue",
        setting_type as "settingType",
        category,
        display_order as "displayOrder",
        is_active as "isActive",
        created_at as "createdAt",
        updated_at as "updatedAt",
        created_by_admin as "createdByAdmin",
        updated_by_admin as "updatedByAdmin"
      FROM contact_settings 
      WHERE setting_key = $1
    `, [settingKey]);
        return result.rows[0] || null;
    }
    /**
     * Create a new contact setting
     */
    static async create(data) {
        const result = await (0, database_1.query)(`
      INSERT INTO contact_settings (
        setting_key, setting_value, setting_type, category, 
        display_order, is_active, created_by_admin
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING 
        id,
        setting_key as "settingKey",
        setting_value as "settingValue",
        setting_type as "settingType",
        category,
        display_order as "displayOrder",
        is_active as "isActive",
        created_at as "createdAt",
        updated_at as "updatedAt",
        created_by_admin as "createdByAdmin",
        updated_by_admin as "updatedByAdmin"
    `, [
            data.settingKey,
            data.settingValue,
            data.settingType,
            data.category,
            data.displayOrder || 0,
            data.isActive !== false,
            data.createdByAdmin
        ]);
        return result.rows[0];
    }
    /**
     * Update a contact setting
     */
    static async update(id, data) {
        const fields = [];
        const values = [];
        let paramIndex = 1;
        if (data.settingValue !== undefined) {
            fields.push(`setting_value = $${paramIndex++}`);
            values.push(data.settingValue);
        }
        if (data.settingType !== undefined) {
            fields.push(`setting_type = $${paramIndex++}`);
            values.push(data.settingType);
        }
        if (data.category !== undefined) {
            fields.push(`category = $${paramIndex++}`);
            values.push(data.category);
        }
        if (data.displayOrder !== undefined) {
            fields.push(`display_order = $${paramIndex++}`);
            values.push(data.displayOrder);
        }
        if (data.isActive !== undefined) {
            fields.push(`is_active = $${paramIndex++}`);
            values.push(data.isActive);
        }
        if (data.updatedByAdmin !== undefined) {
            fields.push(`updated_by_admin = $${paramIndex++}`);
            values.push(data.updatedByAdmin);
        }
        if (fields.length === 0) {
            return null;
        }
        values.push(id);
        const result = await (0, database_1.query)(`
      UPDATE contact_settings 
      SET ${fields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING 
        id,
        setting_key as "settingKey",
        setting_value as "settingValue",
        setting_type as "settingType",
        category,
        display_order as "displayOrder",
        is_active as "isActive",
        created_at as "createdAt",
        updated_at as "updatedAt",
        created_by_admin as "createdByAdmin",
        updated_by_admin as "updatedByAdmin"
    `, values);
        return result.rows[0] || null;
    }
    /**
     * Delete a contact setting (soft delete by setting isActive to false)
     */
    static async delete(id, deletedByAdmin) {
        const result = await (0, database_1.query)(`
      UPDATE contact_settings 
      SET is_active = false, updated_by_admin = $2
      WHERE id = $1
    `, [id, deletedByAdmin]);
        return (result.rowCount || 0) > 0;
    }
    /**
     * Bulk update contact settings
     */
    static async bulkUpdate(updates, updatedByAdmin) {
        const results = [];
        for (const update of updates) {
            const result = await this.update(update.id, {
                settingValue: update.settingValue,
                updatedByAdmin
            });
            if (result) {
                results.push(result);
            }
        }
        return results;
    }
    /**
     * Get public contact settings (for frontend display)
     * Returns only active settings without admin metadata
     */
    static async getPublicSettings() {
        const result = await (0, database_1.query)(`
      SELECT setting_key, setting_value
      FROM contact_settings
      WHERE is_active = true
      ORDER BY category, display_order ASC
    `);
        const settings = {};
        result.rows.forEach(row => {
            settings[row.setting_key] = row.setting_value;
        });
        return settings;
    }
}
exports.ContactSettingsModel = ContactSettingsModel;
