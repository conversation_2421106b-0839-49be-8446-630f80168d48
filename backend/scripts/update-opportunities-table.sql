-- Update opportunities table to match scholarship structure
-- Add missing fields that scholarships have

-- Add slug field for SEO-friendly URLs
ALTER TABLE opportunities ADD COLUMN IF NOT EXISTS slug VARCHAR(255) UNIQUE;

-- Add featured field for highlighting special opportunities
ALTER TABLE opportunities ADD COLUMN IF NOT EXISTS featured BOOLEAN DEFAULT FALSE;

-- Add view_count field for tracking popularity
ALTER TABLE opportunities ADD COLUMN IF NOT EXISTS view_count INTEGER DEFAULT 0;

-- Add status field for better content management
ALTER TABLE opportunities ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'published' CHECK (status IN ('draft', 'published', 'archived'));

-- Add meta fields for SEO
ALTER TABLE opportunities ADD COLUMN IF NOT EXISTS meta_title VARCHAR(255);
ALTER TABLE opportunities ADD COLUMN IF NOT EXISTS meta_description TEXT;

-- Add priority field for sorting
ALTER TABLE opportunities ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 0;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_opportunities_slug ON opportunities(slug);
CREATE INDEX IF NOT EXISTS idx_opportunities_featured ON opportunities(featured);
CREATE INDEX IF NOT EXISTS idx_opportunities_status ON opportunities(status);
CREATE INDEX IF NOT EXISTS idx_opportunities_priority ON opportunities(priority);
CREATE INDEX IF NOT EXISTS idx_opportunities_view_count ON opportunities(view_count);
CREATE INDEX IF NOT EXISTS idx_opportunities_type ON opportunities(type);
CREATE INDEX IF NOT EXISTS idx_opportunities_deadline ON opportunities(deadline);
CREATE INDEX IF NOT EXISTS idx_opportunities_is_active ON opportunities(is_active);

-- Generate slugs for existing opportunities
UPDATE opportunities 
SET slug = LOWER(REGEXP_REPLACE(REGEXP_REPLACE(title, '[^a-zA-Z0-9\s-]', '', 'g'), '\s+', '-', 'g')) || '-' || id
WHERE slug IS NULL;

-- Set some opportunities as featured for testing
UPDATE opportunities 
SET featured = TRUE 
WHERE id IN (
    SELECT id FROM opportunities 
    WHERE type IN ('training', 'internship') 
    ORDER BY created_at DESC 
    LIMIT 3
);
