/**
 * <PERSON><PERSON><PERSON> to update opportunities table structure to match scholarships
 * Run with: node scripts/update-opportunities-structure.js
 */

const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

async function updateOpportunitiesStructure() {
  const client = await pool.connect();
  
  try {
    console.log('Updating opportunities table structure...');
    
    // Add missing fields
    await client.query(`
      ALTER TABLE opportunities ADD COLUMN IF NOT EXISTS slug VARCHAR(255) UNIQUE;
    `);
    console.log('Added slug column');
    
    await client.query(`
      ALTER TABLE opportunities ADD COLUMN IF NOT EXISTS featured BOOLEAN DEFAULT FALSE;
    `);
    console.log('Added featured column');
    
    await client.query(`
      ALTER TABLE opportunities ADD COLUMN IF NOT EXISTS view_count INTEGER DEFAULT 0;
    `);
    console.log('Added view_count column');
    
    await client.query(`
      ALTER TABLE opportunities ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'published' 
      CHECK (status IN ('draft', 'published', 'archived'));
    `);
    console.log('Added status column');
    
    await client.query(`
      ALTER TABLE opportunities ADD COLUMN IF NOT EXISTS meta_title VARCHAR(255);
    `);
    console.log('Added meta_title column');
    
    await client.query(`
      ALTER TABLE opportunities ADD COLUMN IF NOT EXISTS meta_description TEXT;
    `);
    console.log('Added meta_description column');
    
    await client.query(`
      ALTER TABLE opportunities ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 0;
    `);
    console.log('Added priority column');
    
    // Create indexes
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_opportunities_slug ON opportunities(slug);
    `);
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_opportunities_featured ON opportunities(featured);
    `);
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_opportunities_status ON opportunities(status);
    `);
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_opportunities_priority ON opportunities(priority);
    `);
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_opportunities_view_count ON opportunities(view_count);
    `);
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_opportunities_type ON opportunities(type);
    `);
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_opportunities_deadline ON opportunities(deadline);
    `);
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_opportunities_is_active ON opportunities(is_active);
    `);
    
    console.log('Created indexes');
    
    // Generate slugs for existing opportunities
    const opportunities = await client.query('SELECT id, title FROM opportunities WHERE slug IS NULL');
    
    for (const opp of opportunities.rows) {
      const slug = opp.title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim('-') + '-' + opp.id;
      
      await client.query('UPDATE opportunities SET slug = $1 WHERE id = $2', [slug, opp.id]);
    }
    
    console.log(`Generated slugs for ${opportunities.rows.length} opportunities`);
    
    // Set some opportunities as featured
    await client.query(`
      UPDATE opportunities 
      SET featured = TRUE 
      WHERE id IN (
        SELECT id FROM opportunities 
        WHERE type IN ('training', 'internship') 
        ORDER BY created_at DESC 
        LIMIT 3
      )
    `);
    
    console.log('Set featured opportunities');
    
    console.log('Successfully updated opportunities table structure!');
    
  } catch (error) {
    console.error('Error updating opportunities structure:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the script
updateOpportunitiesStructure();
