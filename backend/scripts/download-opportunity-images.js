const fs = require('fs');
const path = require('path');
const https = require('https');
const { Pool } = require('pg');

// Database connection
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'mabourse',
  password: process.env.DB_PASSWORD || 'password',
  port: process.env.DB_PORT || 5432,
});

// Create directories if they don't exist
const uploadsDir = path.join(__dirname, '../../public/uploads/opportunities');
const thumbnailsDir = path.join(uploadsDir, 'thumbnails');

if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}
if (!fs.existsSync(thumbnailsDir)) {
  fs.mkdirSync(thumbnailsDir, { recursive: true });
}

// Production-ready opportunity images (local files)
const opportunityImages = {
  internship: [
    'internship-1.jpg',
    'internship-2.jpg', 
    'internship-3.jpg',
    'internship-4.jpg',
    'internship-5.jpg'
  ],
  training: [
    'training-1.jpg',
    'training-2.jpg',
    'training-3.jpg', 
    'training-4.jpg',
    'training-5.jpg'
  ],
  conference: [
    'conference-1.jpg',
    'conference-2.jpg',
    'conference-3.jpg',
    'conference-4.jpg',
    'conference-5.jpg'
  ],
  workshop: [
    'workshop-1.jpg',
    'workshop-2.jpg',
    'workshop-3.jpg',
    'workshop-4.jpg',
    'workshop-5.jpg'
  ],
  competition: [
    'competition-1.jpg',
    'competition-2.jpg',
    'competition-3.jpg',
    'competition-4.jpg',
    'competition-5.jpg'
  ]
};

// Download image from URL
function downloadImage(url, filepath) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(filepath);
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download image: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        resolve(filepath);
      });
      
      file.on('error', (err) => {
        fs.unlink(filepath, () => {}); // Delete the file on error
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

// High-quality professional images for each opportunity type
const imageUrls = {
  internship: [
    'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1556761175-b413da4baf72?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1553877522-43269d4ea984?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
  ],
  training: [
    'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1531482615713-2afd69097998?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
  ],
  conference: [
    'https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1475721027785-f74eccf877e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1511578314322-379afb476865?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1505373877841-8d25f7d46678?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1591115765373-5207764f72e7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
  ],
  workshop: [
    'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1573164713714-d95e436ab8d6?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
  ],
  competition: [
    'https://images.unsplash.com/photo-1567427017947-545c5f8d16ad?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1586953208448-b95a79798f07?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1504384308090-c894fdcc538d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1551836022-deb4988cc6c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
  ]
};

async function downloadAllImages() {
  console.log('Starting to download opportunity images...');
  
  try {
    // Download images for each type
    for (const [type, urls] of Object.entries(imageUrls)) {
      console.log(`\nDownloading ${type} images...`);
      
      for (let i = 0; i < urls.length; i++) {
        const url = urls[i];
        const filename = opportunityImages[type][i];
        const filepath = path.join(uploadsDir, filename);
        
        try {
          console.log(`Downloading ${filename}...`);
          await downloadImage(url, filepath);
          console.log(`✓ Downloaded ${filename}`);
        } catch (error) {
          console.error(`✗ Failed to download ${filename}:`, error.message);
        }
      }
    }
    
    console.log('\n✅ Image download completed!');
    
  } catch (error) {
    console.error('Error downloading images:', error);
  }
}

async function updateOpportunityImages() {
  const client = await pool.connect();
  
  try {
    console.log('\nUpdating opportunity image paths in database...');
    
    // Get all opportunities
    const result = await client.query('SELECT id, type FROM opportunities');
    const opportunities = result.rows;
    
    console.log(`Found ${opportunities.length} opportunities to update`);
    
    for (const opportunity of opportunities) {
      const { id, type } = opportunity;
      const images = opportunityImages[type] || opportunityImages.internship;
      const randomImage = images[Math.floor(Math.random() * images.length)];
      const imagePath = `/uploads/opportunities/${randomImage}`;
      
      await client.query(
        'UPDATE opportunities SET thumbnail = $1 WHERE id = $2',
        [imagePath, id]
      );
      
      console.log(`Updated opportunity ${id} (${type}): ${imagePath}`);
    }
    
    console.log('✅ Database updated successfully!');
    
  } catch (error) {
    console.error('Error updating database:', error);
  } finally {
    client.release();
  }
}

async function main() {
  try {
    // First download all images
    await downloadAllImages();
    
    // Then update database with local paths
    await updateOpportunityImages();
    
    console.log('\n🎉 All done! Opportunity images are now production-ready.');
    
  } catch (error) {
    console.error('Script failed:', error);
  } finally {
    await pool.end();
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { downloadAllImages, updateOpportunityImages };
