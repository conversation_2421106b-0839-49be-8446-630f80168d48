/**
 * <PERSON><PERSON><PERSON> to add sample opportunities data for testing
 * Run with: node scripts/add-sample-opportunities.js
 */

const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

const sampleOpportunities = [
  // Training opportunities
  {
    title: 'Formation en Intelligence Artificielle et Machine Learning',
    description: 'Formation intensive de 6 mois en IA et ML avec certification professionnelle. Apprenez les dernières technologies et techniques d\'apprentissage automatique.',
    type: 'training',
    organization: 'TechAcademy Paris',
    location: 'Paris, France',
    isRemote: false,
    deadline: '2024-12-15',
    startDate: '2025-01-15',
    endDate: '2025-07-15',
    applicationLink: 'https://techacademy.fr/ai-formation',
    thumbnail: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
    isActive: true,
    tags: ['IA', 'Machine Learning', 'Python', 'Data Science']
  },
  {
    title: 'Formation Développement Web Full-Stack',
    description: 'Devenez développeur full-stack en 4 mois. Formation complète couvrant React, Node.js, bases de données et déploiement.',
    type: 'training',
    organization: 'WebDev Institute',
    location: 'Lyon, France',
    isRemote: true,
    deadline: '2024-11-30',
    startDate: '2025-01-08',
    endDate: '2025-05-08',
    applicationLink: 'https://webdev-institute.fr/fullstack',
    thumbnail: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
    isActive: true,
    tags: ['React', 'Node.js', 'JavaScript', 'Full-Stack']
  },
  {
    title: 'Formation Cybersécurité et Ethical Hacking',
    description: 'Formation spécialisée en cybersécurité avec focus sur le ethical hacking et la protection des systèmes d\'information.',
    type: 'training',
    organization: 'CyberSec Academy',
    location: 'Toulouse, France',
    isRemote: false,
    deadline: '2024-12-20',
    startDate: '2025-02-01',
    endDate: '2025-08-01',
    applicationLink: 'https://cybersec-academy.fr/ethical-hacking',
    thumbnail: 'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
    isActive: true,
    tags: ['Cybersécurité', 'Ethical Hacking', 'Sécurité', 'Réseaux']
  },

  // Internship opportunities
  {
    title: 'Stage Développeur Frontend React - 6 mois',
    description: 'Rejoignez notre équipe de développement pour un stage de 6 mois. Travaillez sur des projets innovants avec React, TypeScript et les dernières technologies.',
    type: 'internship',
    organization: 'TechCorp Solutions',
    location: 'Paris, France',
    isRemote: false,
    deadline: '2024-12-01',
    startDate: '2025-02-01',
    endDate: '2025-08-01',
    applicationLink: 'https://techcorp.fr/careers/stage-frontend',
    thumbnail: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
    isActive: true,
    tags: ['React', 'Frontend', 'TypeScript', 'Stage']
  },
  {
    title: 'Stage Data Scientist - Analyse Prédictive',
    description: 'Stage de 4 mois en data science avec focus sur l\'analyse prédictive et le machine learning appliqué aux données business.',
    type: 'internship',
    organization: 'DataLab Analytics',
    location: 'Marseille, France',
    isRemote: true,
    deadline: '2024-11-25',
    startDate: '2025-01-15',
    endDate: '2025-05-15',
    applicationLink: 'https://datalab.fr/stage-data-scientist',
    thumbnail: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
    isActive: true,
    tags: ['Data Science', 'Python', 'Machine Learning', 'Analytics']
  },
  {
    title: 'Stage Marketing Digital et Growth Hacking',
    description: 'Stage de 5 mois dans une startup en croissance. Apprenez les techniques de growth hacking et de marketing digital moderne.',
    type: 'internship',
    organization: 'GrowthStart',
    location: 'Bordeaux, France',
    isRemote: false,
    deadline: '2024-12-10',
    startDate: '2025-01-20',
    endDate: '2025-06-20',
    applicationLink: 'https://growthstart.fr/stage-marketing',
    thumbnail: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
    isActive: true,
    tags: ['Marketing Digital', 'Growth Hacking', 'SEO', 'Analytics']
  },

  // Conference opportunities
  {
    title: 'Conférence Internationale sur l\'IA et l\'Éthique',
    description: 'Participez à la plus grande conférence européenne sur l\'intelligence artificielle et l\'éthique. Rencontrez les leaders du domaine.',
    type: 'conference',
    organization: 'AI Ethics Europe',
    location: 'Berlin, Allemagne',
    isRemote: false,
    deadline: '2024-11-15',
    startDate: '2025-03-15',
    endDate: '2025-03-17',
    applicationLink: 'https://ai-ethics-europe.org/conference2025',
    thumbnail: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
    isActive: true,
    tags: ['IA', 'Éthique', 'Conférence', 'Networking']
  },
  {
    title: 'Summit Européen de la Cybersécurité 2025',
    description: 'Le plus grand rassemblement de professionnels de la cybersécurité en Europe. Sessions techniques, ateliers et networking.',
    type: 'conference',
    organization: 'CyberSec Europe',
    location: 'Amsterdam, Pays-Bas',
    isRemote: true,
    deadline: '2024-12-05',
    startDate: '2025-04-10',
    endDate: '2025-04-12',
    applicationLink: 'https://cybersec-summit.eu/2025',
    thumbnail: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
    isActive: true,
    tags: ['Cybersécurité', 'Summit', 'Networking', 'Europe']
  },

  // Workshop opportunities
  {
    title: 'Atelier Pratique: Développement d\'APIs REST avec Node.js',
    description: 'Atelier hands-on de 2 jours pour apprendre à développer des APIs REST robustes et sécurisées avec Node.js et Express.',
    type: 'workshop',
    organization: 'DevWorkshop Pro',
    location: 'Nice, France',
    isRemote: false,
    deadline: '2024-11-20',
    startDate: '2025-01-25',
    endDate: '2025-01-26',
    applicationLink: 'https://devworkshop.pro/nodejs-api',
    thumbnail: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
    isActive: true,
    tags: ['Node.js', 'API', 'REST', 'Backend']
  },
  {
    title: 'Atelier Design Thinking et Innovation',
    description: 'Atelier interactif de 3 jours sur les méthodes de design thinking appliquées à l\'innovation produit et service.',
    type: 'workshop',
    organization: 'Innovation Lab',
    location: 'Lille, France',
    isRemote: true,
    deadline: '2024-12-01',
    startDate: '2025-02-15',
    endDate: '2025-02-17',
    applicationLink: 'https://innovation-lab.fr/design-thinking',
    thumbnail: 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
    isActive: true,
    tags: ['Design Thinking', 'Innovation', 'Créativité', 'Méthodes']
  },

  // Competition opportunities
  {
    title: 'Hackathon National FinTech 2025',
    description: 'Compétition de 48h pour développer des solutions innovantes dans le domaine de la FinTech. Prix de 50 000€ à gagner.',
    type: 'competition',
    organization: 'FinTech France',
    location: 'Paris, France',
    isRemote: false,
    deadline: '2024-12-31',
    startDate: '2025-03-01',
    endDate: '2025-03-03',
    applicationLink: 'https://fintech-france.org/hackathon2025',
    thumbnail: 'https://images.unsplash.com/photo-1556155092-490a1ba16284?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
    isActive: true,
    tags: ['Hackathon', 'FinTech', 'Innovation', 'Compétition']
  },
  {
    title: 'Concours Startup Étudiante 2025',
    description: 'Concours national pour les projets de startup étudiante. Accompagnement, mentorat et financement pour les gagnants.',
    type: 'competition',
    organization: 'Startup Campus',
    location: 'Lyon, France',
    isRemote: false,
    deadline: '2024-11-30',
    startDate: '2025-01-15',
    endDate: '2025-06-15',
    applicationLink: 'https://startup-campus.fr/concours2025',
    thumbnail: 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
    isActive: true,
    tags: ['Startup', 'Entrepreneuriat', 'Étudiants', 'Innovation']
  }
];

async function addSampleOpportunities() {
  const client = await pool.connect();
  
  try {
    console.log('Adding sample opportunities...');
    
    for (const opportunity of sampleOpportunities) {
      const query = `
        INSERT INTO opportunities (
          title, description, type, organization, location, is_remote,
          deadline, start_date, end_date, application_link, thumbnail,
          is_active, tags, created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, NOW(), NOW()
        )
      `;
      
      const values = [
        opportunity.title,
        opportunity.description,
        opportunity.type,
        opportunity.organization,
        opportunity.location,
        opportunity.isRemote,
        opportunity.deadline,
        opportunity.startDate,
        opportunity.endDate,
        opportunity.applicationLink,
        opportunity.thumbnail,
        opportunity.isActive,
        JSON.stringify(opportunity.tags)
      ];
      
      await client.query(query, values);
      console.log(`Added: ${opportunity.title}`);
    }
    
    console.log(`Successfully added ${sampleOpportunities.length} sample opportunities!`);
    
  } catch (error) {
    console.error('Error adding sample opportunities:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the script
addSampleOpportunities();
