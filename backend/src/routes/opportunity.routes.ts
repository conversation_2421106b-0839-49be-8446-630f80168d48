/**
 * Opportunity Routes
 * 
 * API routes for opportunity management with proper authentication and validation
 */

import { Router } from 'express';
import {
  getAllOpportunities,
  getActiveOpportunities,
  getOpportunityById,
  getOpportunityBySlug,
  getOpportunitiesByType,
  searchOpportunities,
  createOpportunity,
  updateOpportunity,
  deleteOpportunity,
  getOpportunityTypes,
  getLatestOpportunities,
  getOpportunityTypesForSidebar
} from '../controllers/opportunity.controller';
import { requireAdmin } from '../middleware/auth.new';

const router = Router();

// Public routes
router.get('/', getAllOpportunities);
router.get('/active', getActiveOpportunities);
router.get('/search', searchOpportunities);
router.get('/types', getOpportunityTypes);
router.get('/latest', getLatestOpportunities);
router.get('/types-sidebar', getOpportunityTypesForSidebar);
router.get('/type/:type', getOpportunitiesByType);
router.get('/slug/:slug', getOpportunityBySlug);
router.get('/:id', getOpportunityById);

// Admin-only routes
router.post('/', requireAdmin, createOpportunity);
router.put('/:id', requireAdmin, updateOpportunity);
router.delete('/:id', requireAdmin, deleteOpportunity);

export default router;
