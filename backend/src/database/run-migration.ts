import { query, initializeDatabase } from '../config/database';
import fs from 'fs';
import path from 'path';

async function runMigration() {
  try {
    console.log('Initializing database connection...');
    await initializeDatabase();

    console.log('Running French sections migration...');

    const migrationPath = path.join(__dirname, 'migrations', '005_add_french_sections_to_scholarships.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Execute the entire migration as one statement
    console.log('Executing migration...');
    await query(migrationSQL);

    console.log('Migration completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

runMigration();
