-- Migration: Add French sections to scholarships table
-- Version: 005
-- Date: 2025-01-12
-- Description: Add new fields for French scholarship sections

-- Add new columns to scholarships table
ALTER TABLE scholarships
ADD COLUMN IF NOT EXISTS domaines_etudes TEXT,
ADD COLUMN IF NOT EXISTS universites_participantes TEXT,
ADD COLUMN IF NOT EXISTS documents_requis TEXT,
ADD COLUMN IF NOT EXISTS conditions_eligibilite TEXT,
ADD COLUMN IF NOT EXISTS lien_postuler VARCHAR(500),
ADD COLUMN IF NOT EXISTS chaine_youtube_reseaux TEXT;

-- Update existing scholarships with sample data for testing
UPDATE scholarships 
SET 
    domaines_etudes = CASE 
        WHEN level = 'Graduate' THEN 'Sciences, Ingénierie, Médecine, Sciences Sociales, Arts et Humanités. Cette bourse est ouverte à tous les domaines d''études avec une préférence pour les programmes de recherche innovants.'
        WHEN level = 'Master''s' THEN 'Business, Management, Sciences Politiques, Relations Internationales, Économie. Focus sur les programmes qui développent les compétences de leadership.'
        WHEN level = 'PhD' THEN 'Recherche en Sciences, Technologie, Médecine, Sciences Sociales. Programmes de doctorat avec composante recherche obligatoire.'
        ELSE 'Tous les domaines d''études sont acceptés selon les critères d''éligibilité de chaque université participante.'
    END,
    universites_participantes = CASE 
        WHEN country = 'United States' THEN 'Harvard University, MIT, Stanford University, Yale University, Princeton University, Columbia University, University of Chicago, et plus de 150 autres universités américaines accréditées.'
        WHEN country = 'United Kingdom' THEN 'Oxford University, Cambridge University, Imperial College London, LSE, King''s College London, University of Edinburgh, et toutes les universités britanniques reconnues.'
        WHEN country = 'Germany' THEN 'Max Planck Institutes, Technical University of Munich, Heidelberg University, Humboldt University, RWTH Aachen, et plus de 100 universités allemandes.'
        ELSE 'Universités publiques et privées accréditées dans le pays d''accueil, selon les accords bilatéraux et les critères de qualité académique.'
    END,
    documents_requis = 'Diplômes et relevés de notes officiels traduits et certifiés, Lettres de recommandation (2-3) de professeurs ou employeurs, CV académique détaillé, Lettre de motivation personnalisée, Certificats de compétence linguistique (TOEFL/IELTS/DELF selon le pays), Projet de recherche ou d''études (selon le niveau), Passeport valide, Photos d''identité récentes, Certificats médicaux si requis.',
    conditions_eligibilite = CASE 
        WHEN level = 'Graduate' THEN 'Diplôme de licence avec mention (minimum 14/20 ou équivalent), Maîtrise de la langue d''enseignement, Âge maximum 35 ans, Nationalité des pays éligibles, Engagement à retourner dans le pays d''origine après les études, Aucune autre bourse en cours.'
        WHEN level = 'Master''s' THEN 'Diplôme de licence dans un domaine pertinent, Expérience professionnelle de 2-3 ans minimum, Compétences linguistiques avancées, Leadership démontré, Âge entre 25-35 ans, Engagement communautaire.'
        WHEN level = 'PhD' THEN 'Master ou équivalent avec excellents résultats, Projet de recherche approuvé, Directeur de thèse identifié, Publications ou expérience de recherche, Compétences linguistiques excellentes, Âge maximum 40 ans.'
        ELSE 'Critères généraux : Excellence académique, Motivation claire, Compétences linguistiques, Situation financière justifiée, Engagement à respecter les conditions de la bourse.'
    END,
    lien_postuler = COALESCE(scholarship_link, 'https://example.com/apply'),
    chaine_youtube_reseaux = 'Chaîne YouTube MaBourse : https://youtube.com/@mabourse - Tutoriels complets sur les candidatures
Facebook : https://facebook.com/mabourse - Actualités et conseils quotidiens  
Instagram : @mabourse_officiel - Stories et témoignages d''étudiants
LinkedIn : MaBourse Network - Réseau professionnel et opportunités
Telegram : @MaBourseAlerts - Alertes en temps réel sur les nouvelles bourses'
WHERE domaines_etudes IS NULL;

-- Create indexes for better performance on new fields
CREATE INDEX IF NOT EXISTS idx_scholarships_domaines_etudes ON scholarships USING gin(to_tsvector('french', domaines_etudes));
CREATE INDEX IF NOT EXISTS idx_scholarships_universites ON scholarships USING gin(to_tsvector('french', universites_participantes));
CREATE INDEX IF NOT EXISTS idx_scholarships_lien_postuler ON scholarships(lien_postuler);

-- Add comments for documentation
COMMENT ON COLUMN scholarships.domaines_etudes IS 'Domaines d''études acceptés pour cette bourse (texte libre en français)';
COMMENT ON COLUMN scholarships.universites_participantes IS 'Liste des universités participantes (texte libre en français)';
COMMENT ON COLUMN scholarships.documents_requis IS 'Documents requis pour la candidature (texte libre en français)';
COMMENT ON COLUMN scholarships.conditions_eligibilite IS 'Conditions d''éligibilité détaillées (texte libre en français)';
COMMENT ON COLUMN scholarships.lien_postuler IS 'Lien direct pour postuler à la bourse';
COMMENT ON COLUMN scholarships.chaine_youtube_reseaux IS 'Liens vers chaîne YouTube et réseaux sociaux (texte libre en français)';
