@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Import Professional Animations */
@import './styles/animations.css';

/* Import Cross-Browser Compatibility */
@import './styles/browser-compatibility.css';

/* Import GreatYOP Card Styles */
@import './styles/greatyop-cards.css';

/* Import GreatYOP Typography */
@import './styles/greatyop-typography.css';

/* Import FontAwesome */
@import '~@fortawesome/fontawesome-free/css/all.css';

/* RTL Support */
.rtl {
  direction: rtl;
  text-align: right;
}

.ltr {
  direction: ltr;
  text-align: left;
}

/* RTL-specific styles */
.rtl .space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

.rtl .space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

.rtl .space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

/* Ensure proper text alignment in RTL */
.rtl p, .rtl h1, .rtl h2, .rtl h3, .rtl h4, .rtl h5, .rtl h6 {
  text-align: right;
}

/* Ensure proper margin and padding in RTL */
.rtl .ml-auto {
  margin-left: 0;
  margin-right: auto;
}

.rtl .mr-auto {
  margin-right: 0;
  margin-left: auto;
}

/* Ensure proper flex direction in RTL */
.rtl .flex-row {
  flex-direction: row-reverse;
}

/* Ensure proper grid flow in RTL */
.rtl .grid-flow-row {
  grid-auto-flow: row-reverse;
}

/* Custom animations */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

@keyframes pulse-soft {
  0% { box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(var(--color-primary-rgb), 0); }
  100% { box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0); }
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

/* CSS Variables for consistent design system */
:root {
  --color-primary: #2563eb;
  --color-primary-rgb: 37, 99, 235;
  --color-primary-dark: #1d4ed8;
  --color-primary-light: #3b82f6;
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;

  --color-secondary: #8b5cf6;
  --color-secondary-rgb: 139, 92, 246;
  --color-secondary-dark: #7c3aed;
  --color-secondary-light: #a78bfa;

  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  --border-radius-sm: 0.25rem;
  --border-radius: 0.375rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  --border-radius-2xl: 1.5rem;
  --border-radius-3xl: 2rem;
  --border-radius-full: 9999px;
}

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-gray-50;
    color: #3d3d3d;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif;
    font-size: 17px;
    line-height: 30px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold tracking-tight;
  }

  /* Focus styles for accessibility */
  *:focus-visible {
    @apply outline-none ring-2 ring-primary ring-offset-2 ring-offset-white;
  }
}

@layer components {
  /* Aspect ratio utilities */
  .aspect-w-16 {
    position: relative;
    padding-bottom: calc(var(--tw-aspect-h) / var(--tw-aspect-w) * 100%);
    --tw-aspect-w: 16;
  }

  .aspect-h-9 {
    --tw-aspect-h: 9;
  }

  .aspect-w-16 > * {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }

  /* Button styles */
  .btn {
    @apply inline-flex items-center justify-center px-5 py-3 rounded-lg font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply bg-primary text-white hover:bg-primary-dark focus:ring-primary shadow-md hover:shadow-lg;
  }

  .btn-secondary {
    @apply bg-secondary text-white hover:bg-secondary-dark focus:ring-secondary shadow-md hover:shadow-lg;
  }

  .btn-outline {
    @apply border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-gray-500;
  }

  .btn-sm {
    @apply px-3 py-2 text-sm;
  }

  .btn-lg {
    @apply px-6 py-4 text-lg;
  }

  /* Card styles */
  .card {
    @apply bg-white rounded-xl shadow-md overflow-hidden;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
  }

  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }

  .badge-secondary {
    @apply bg-purple-100 text-purple-800;
  }

  .badge-success {
    @apply bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .badge-error {
    @apply bg-red-100 text-red-800;
  }

  /* Section styles */
  .section {
    @apply py-16 md:py-24;
  }

  .section-title {
    @apply text-3xl md:text-4xl font-bold text-gray-900 mb-4;
  }

  .section-subtitle {
    @apply text-xl text-gray-600 max-w-3xl;
  }

  /* Animation utility classes */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-soft {
    animation: pulse-soft 2s infinite;
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out forwards;
  }

  /* Loading skeleton */
  .skeleton {
    @apply bg-gray-200 rounded;
    background-image: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0),
      rgba(255, 255, 255, 0.5),
      rgba(255, 255, 255, 0)
    );
    background-size: 40px 100%;
    background-repeat: no-repeat;
    background-position: left -40px top 0;
    animation: shimmer 2s infinite;
  }

  /* Glass effect */
  .glass {
    @apply backdrop-blur-md bg-white/70 border border-white/20;
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-gray-400 rounded-full hover:bg-gray-500;
  }
}