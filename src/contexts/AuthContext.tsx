import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import authService from '../services/authService';

// Types
interface Admin {
  id: number;
  name: string;
  email: string;
  role: string;
  isMainAdmin: boolean;
  privileges: string[];
  twoFactorEnabled: boolean;
}

interface AuthContextType {
  // State
  admin: Admin | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  clearError: () => void;
  refreshAuth: () => Promise<void>;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [admin, setAdmin] = useState<Admin | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Clear error
  const clearError = () => setError(null);

  // Check authentication status on mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  // Check authentication status
  const checkAuthStatus = async () => {
    try {
      setIsLoading(true);
      const response = await authService.getAdminProfile();

      if (response.success && response.data?.admin) {
        setAdmin(response.data.admin);
        setIsAuthenticated(true);
      } else {
        setAdmin(null);
        setIsAuthenticated(false);
      }
    } catch (error: any) {
      // Handle authentication errors gracefully
      console.log('Auth check failed (this is normal for unauthenticated users):', error.message);
      setAdmin(null);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  // Login function
  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await authService.adminLogin(email, password);
      
      if (response.success && response.data?.admin) {
        setAdmin(response.data.admin);
        setIsAuthenticated(true);
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error: any) {
      setError(error.message);
      setAdmin(null);
      setIsAuthenticated(false);
      throw error; // Re-throw for component handling
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      setIsLoading(true);
      // Clear local state first to prevent UI flickering
      setAdmin(null);
      setIsAuthenticated(false);

      // Then attempt server-side logout
      await authService.adminLogout();
    } catch (error: any) {
      console.error('Logout error:', error);
      // Continue with logout even if API call fails
      // Local state is already cleared above
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh authentication status
  const refreshAuth = async () => {
    await checkAuthStatus();
  };

  const value: AuthContextType = {
    admin,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    clearError,
    refreshAuth
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
