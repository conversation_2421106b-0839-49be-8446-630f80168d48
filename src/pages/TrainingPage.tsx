import React from 'react';
import StandardOpportunityPage from '../components/StandardOpportunityPage';

const TrainingPage: React.FC = () => {
  const pageConfig = {
    type: 'training',
    title: 'Formations Professionnelles - Développez Vos Compétences | MaBourse',
    description: 'Découvrez les meilleures formations professionnelles disponibles. Développez vos compétences avec des programmes de formation de qualité dans tous les domaines.',
    keywords: 'formations professionnelles, développement compétences, formation continue, apprentissage, certification professionnelle',

    heroTitle: 'Formations Professionnelles',
    heroDescription: 'Découvrez notre collection exclusive de formations professionnelles. Ces opportunités exceptionnelles sont proposées par des institutions de formation prestigieuses, des universités de renommée mondiale, et des organismes dédiés à l\'excellence éducative.',
    
    infoTitle: 'Pourquoi Choisir une Formation Professionnelle ?',
    infoContent: 'Que vous visiez le développement technologique, la gestion d\'entreprise, les compétences numériques, ou les secteurs innovants, chaque formation offre un apprentissage pratique adapté à vos ambitions professionnelles. Explorez, comparez et trouvez la formation qui transformera votre parcours de carrière.',
    benefits: [
      'Acquisition de compétences pratiques et immédiatement applicables',
      'Certification reconnue par les employeurs',
      'Amélioration de votre employabilité et perspectives de carrière',
      'Accès à des formateurs experts dans leur domaine',
      'Flexibilité des formats : présentiel, distanciel ou hybride',
      'Mise à jour de vos connaissances avec les dernières tendances'
    ],
    apiEndpoint: '/api/opportunities/search'
  };

  return <StandardOpportunityPage config={pageConfig} />;
};

export default TrainingPage;
