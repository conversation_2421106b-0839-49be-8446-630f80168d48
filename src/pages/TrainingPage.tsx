import React from 'react';
import StandardOpportunityPage from '../components/StandardOpportunityPage';

const TrainingPage: React.FC = () => {
  const pageConfig = {
    type: 'training',
    title: 'Formations Professionnelles - Développez Vos Compétences | MaBourse',
    description: 'Découvrez les meilleures formations professionnelles disponibles. Développez vos compétences avec des programmes de formation de qualité dans tous les domaines.',
    keywords: 'formations professionnelles, développement compétences, formation continue, apprentissage, certification professionnelle',
    
    heroTitle: 'Formations Professionnelles',
    heroDescription: 'Développez vos compétences et boostez votre carrière avec nos formations professionnelles sélectionnées. Des programmes de qualité pour tous les niveaux et tous les domaines d\'expertise.',
    
    infoTitle: 'Pourquoi Choisir une Formation Professionnelle ?',
    infoContent: 'Dans un monde professionnel en constante évolution, la formation continue est essentielle pour rester compétitif. Nos formations professionnelles vous permettent d\'acquérir de nouvelles compétences, de vous spécialiser dans votre domaine ou de vous reconvertir vers de nouveaux horizons.',
    benefits: [
      'Acquisition de compétences pratiques et immédiatement applicables',
      'Certification reconnue par les employeurs',
      'Amélioration de votre employabilité et perspectives de carrière',
      'Accès à des formateurs experts dans leur domaine',
      'Flexibilité des formats : présentiel, distanciel ou hybride',
      'Mise à jour de vos connaissances avec les dernières tendances'
    ],
    apiEndpoint: '/api/opportunities/search'
  };

  return <StandardOpportunityPage config={pageConfig} />;
};

export default TrainingPage;
