import React from 'react';
import StandardOpportunityPage from '../components/StandardOpportunityPage';

const WorkshopPage: React.FC = () => {
  const pageConfig = {
    type: 'workshop',
    title: 'Ateliers Pratiques - Apprenez par la Pratique | MaBourse',
    description: 'Participez à des ateliers pratiques pour développer vos compétences. Apprentissage hands-on avec des experts dans un environnement collaboratif.',
    keywords: 'ateliers pratiques, workshop, formation hands-on, apprentissage pratique, compétences techniques',
    
    heroTitle: 'Ateliers Pratiques',
    heroDescription: 'Développez vos compétences à travers des ateliers pratiques intensifs. Apprenez par la pratique avec des experts et dans un environnement collaboratif stimulant.',
    
    infoTitle: 'Pourquoi Participer à des Ateliers ?',
    infoContent: 'Les ateliers offrent une approche d\'apprentissage unique basée sur la pratique et l\'interaction. Contrairement aux formations théoriques, ils vous permettent d\'acquérir des compétences concrètes en travaillant sur des projets réels avec l\'accompagnement d\'experts.',
    benefits: [
      'Apprentissage pratique et immédiat des compétences',
      'Travail collaboratif avec d\'autres participants',
      'Accompagnement personnalisé par des experts',
      'Projets concrets et applicables immédiatement',
      'Environnement d\'apprentissage interactif et stimulant',
      'Certification des compétences acquises'
    ],
    apiEndpoint: '/api/opportunities/search'
  };

  return <StandardOpportunityPage config={pageConfig} />;
};

export default WorkshopPage;
