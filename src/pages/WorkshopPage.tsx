import React from 'react';
import StandardOpportunityPage from '../components/StandardOpportunityPage';

const WorkshopPage: React.FC = () => {
  const pageConfig = {
    type: 'workshop',
    title: 'Ateliers Pratiques - Apprenez par la Pratique | MaBourse',
    description: 'Participez à des ateliers pratiques pour développer vos compétences. Apprentissage hands-on avec des experts dans un environnement collaboratif.',
    keywords: 'ateliers pratiques, workshop, formation hands-on, apprentissage pratique, compétences techniques',
    
    heroTitle: 'Ateliers Pratiques',
    heroDescription: 'Découvrez notre collection exclusive d\'ateliers pratiques. Ces opportunités exceptionnelles sont proposées par des institutions de formation prestigieuses, des universités de renommée mondiale, et des organismes dédiés à l\'excellence éducative.',

    infoTitle: 'Pourquoi Participer à des Ateliers ?',
    infoContent: 'Que vous visiez les ateliers technologiques, les sessions de développement personnel, les workshops créatifs, ou les formations techniques, chaque atelier offre un apprentissage pratique adapté à vos ambitions professionnelles. Explorez, comparez et trouvez l\'atelier qui transformera votre parcours de carrière.',
    benefits: [
      'Apprentissage pratique et immédiat des compétences',
      'Travail collaboratif avec d\'autres participants',
      'Accompagnement personnalisé par des experts',
      'Projets concrets et applicables immédiatement',
      'Environnement d\'apprentissage interactif et stimulant',
      'Certification des compétences acquises'
    ],
    apiEndpoint: '/api/opportunities/search'
  };

  return <StandardOpportunityPage config={pageConfig} />;
};

export default WorkshopPage;
