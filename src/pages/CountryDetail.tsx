import React from 'react';
import { useParams } from 'react-router-dom';
import StandardCountryPage from '../components/StandardCountryPage';

// Helper function to get country flag - Complete World Countries Database
const getCountryFlag = (countryName: string): string => {
  const flagMap: { [key: string]: string } = {
    // === EUROPE ===
    'France': '🇫🇷', 'Allemagne': '🇩🇪', 'Germany': '🇩🇪',
    'Royaume-Uni': '🇬🇧', 'United Kingdom': '🇬🇧', 'UK': '🇬🇧',
    'Italie': '🇮🇹', 'Italy': '🇮🇹',
    'Espagne': '🇪🇸', 'Spain': '🇪🇸',
    'Portugal': '🇵🇹',
    'Pays-Bas': '🇳🇱', 'Netherlands': '🇳🇱',
    'Belgique': '🇧🇪', 'Belgium': '🇧🇪',
    'Suisse': '🇨🇭', 'Switzerland': '🇨🇭',
    'Autriche': '🇦🇹', 'Austria': '🇦🇹',
    'Suède': '🇸🇪', 'Sweden': '🇸🇪',
    'Norvège': '🇳🇴', 'Norway': '🇳🇴',
    'Danemark': '🇩🇰', 'Denmark': '🇩🇰',
    'Finlande': '🇫🇮', 'Finland': '🇫🇮',
    'Pologne': '🇵🇱', 'Poland': '🇵🇱',
    'République tchèque': '🇨🇿', 'Czech Republic': '🇨🇿',
    'Hongrie': '🇭🇺', 'Hungary': '🇭🇺',
    'Roumanie': '🇷🇴', 'Romania': '🇷🇴',
    'Bulgarie': '🇧🇬', 'Bulgaria': '🇧🇬',
    'Croatie': '🇭🇷', 'Croatia': '🇭🇷',
    'Slovénie': '🇸🇮', 'Slovenia': '🇸🇮',
    'Slovaquie': '🇸🇰', 'Slovakia': '🇸🇰',
    'Estonie': '🇪🇪', 'Estonia': '🇪🇪',
    'Lettonie': '🇱🇻', 'Latvia': '🇱🇻',
    'Lituanie': '🇱🇹', 'Lithuania': '🇱🇹',
    'Islande': '🇮🇸', 'Iceland': '🇮🇸',
    'Irlande': '🇮🇪', 'Ireland': '🇮🇪',
    'Grèce': '🇬🇷', 'Greece': '🇬🇷',
    'Chypre': '🇨🇾', 'Cyprus': '🇨🇾',
    'Malte': '🇲🇹', 'Malta': '🇲🇹',
    'Luxembourg': '🇱🇺',
    'Monaco': '🇲🇨',
    'Liechtenstein': '🇱🇮',
    'Saint-Marin': '🇸🇲', 'San Marino': '🇸🇲',
    'Vatican': '🇻🇦',
    'Andorre': '🇦🇩', 'Andorra': '🇦🇩',

    // === NORTH AMERICA ===
    'États-Unis': '🇺🇸', 'United States': '🇺🇸', 'USA': '🇺🇸',
    'Canada': '🇨🇦',
    'Mexique': '🇲🇽', 'Mexico': '🇲🇽',

    // === OCEANIA ===
    'Australie': '🇦🇺', 'Australia': '🇦🇺',
    'Nouvelle-Zélande': '🇳🇿', 'New Zealand': '🇳🇿',
    'Fidji': '🇫🇯', 'Fiji': '🇫🇯',

    // === ASIA ===
    'Japon': '🇯🇵', 'Japan': '🇯🇵',
    'Corée du Sud': '🇰🇷', 'South Korea': '🇰🇷',
    'Chine': '🇨🇳', 'China': '🇨🇳',
    'Singapour': '🇸🇬', 'Singapore': '🇸🇬',
    'Hong Kong': '🇭🇰',
    'Taïwan': '🇹🇼', 'Taiwan': '🇹🇼',
    'Malaisie': '🇲🇾', 'Malaysia': '🇲🇾',
    'Thaïlande': '🇹🇭', 'Thailand': '🇹🇭',
    'Indonésie': '🇮🇩', 'Indonesia': '🇮🇩',
    'Philippines': '🇵🇭',
    'Vietnam': '🇻🇳',
    'Inde': '🇮🇳', 'India': '🇮🇳',

    // === AFRICA ===
    'Afrique du Sud': '🇿🇦', 'South Africa': '🇿🇦',
    'Maroc': '🇲🇦', 'Morocco': '🇲🇦',
    'Tunisie': '🇹🇳', 'Tunisia': '🇹🇳',
    'Algérie': '🇩🇿', 'Algeria': '🇩🇿',
    'Égypte': '🇪🇬', 'Egypt': '🇪🇬',
    'Kenya': '🇰🇪',
    'Ghana': '🇬🇭',
    'Nigeria': '🇳🇬',

    // === SOUTH AMERICA ===
    'Brésil': '🇧🇷', 'Brazil': '🇧🇷',
    'Argentine': '🇦🇷', 'Argentina': '🇦🇷',
    'Chili': '🇨🇱', 'Chile': '🇨🇱',
    'Colombie': '🇨🇴', 'Colombia': '🇨🇴',
    'Pérou': '🇵🇪', 'Peru': '🇵🇪',
    'Uruguay': '🇺🇾',
    'Équateur': '🇪🇨', 'Ecuador': '🇪🇨',
    'Venezuela': '🇻🇪',

    // === MIDDLE EAST ===
    'Israël': '🇮🇱', 'Israel': '🇮🇱',
    'Émirats arabes unis': '🇦🇪', 'UAE': '🇦🇪',
    'Qatar': '🇶🇦',
    'Arabie saoudite': '🇸🇦', 'Saudi Arabia': '🇸🇦',
    'Turquie': '🇹🇷', 'Turkey': '🇹🇷',
    'Liban': '🇱🇧', 'Lebanon': '🇱🇧',
    'Jordanie': '🇯🇴', 'Jordan': '🇯🇴',

    // === OTHER ===
    'Russie': '🇷🇺', 'Russia': '🇷🇺',
    'Ukraine': '🇺🇦',
    'Biélorussie': '🇧🇾', 'Belarus': '🇧🇾',
  };
  return flagMap[countryName] || '🌍';
};

const CountryDetail: React.FC = () => {
  const { country } = useParams<{ country: string }>();
  const decodedCountry = country ? decodeURIComponent(country) : '';

  const pageConfig = {
    country: decodedCountry,
    title: `Bourses d'Études en ${decodedCountry} | Opportunités de Financement`,
    description: `Découvrez toutes les bourses d'études disponibles en ${decodedCountry}. Financez vos études supérieures avec des opportunités de bourses prestigieuses.`,
    keywords: `bourses ${decodedCountry}, financement études ${decodedCountry}, études supérieures ${decodedCountry}`,
    infoTitle: `Pourquoi Étudier en ${decodedCountry} ?`,
    infoContent: `${decodedCountry} offre un système éducatif de qualité mondiale avec des universités prestigieuses et des opportunités de recherche exceptionnelles. Avec une bourse d'études, vous pouvez accéder à cette excellence académique sans contraintes financières.`,
    benefits: [
      'Universités de renommée mondiale',
      'Programmes académiques d\'excellence',
      'Environnement multiculturel enrichissant',
      'Opportunités de recherche avancée',
      'Réseau professionnel international',
      'Expérience culturelle unique'
    ],
    apiEndpoint: '/api/scholarships/search',
    flag: getCountryFlag(decodedCountry)
  };

  return <StandardCountryPage config={pageConfig} />;
};

export default CountryDetail;
