import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { <PERSON><PERSON>, But<PERSON>, Badge } from 'antd';
import { extractIdFromSlug } from '../utils/slugify';
import dateUtils, { DateFormat } from '../utils/dateUtils';
import {
  CalendarOutlined,
  GlobalOutlined,
  BankOutlined,
  BookOutlined,
  CheckCircleOutlined,

  FileTextOutlined,
  LinkOutlined,
  TrophyOutlined,
  UserOutlined,
  YoutubeOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';



// Define the Scholarship interface based on the Prisma schema
interface Scholarship {
  id: number;
  title: string;
  description: string;
  level?: string;
  country?: string;
  deadline: string;
  isOpen: boolean;
  thumbnail?: string;
  coverage?: string;
  financial_benefits_summary?: string;
  eligibility_summary?: string;
  scholarship_link?: string;
  youtube_link?: string;

  // New French sections
  domainesEtudes?: string;
  universitesParticipantes?: string;
  documentsRequis?: string;
  conditionsEligibilite?: string;
  lienPostuler?: string;
  chaineYoutubeReseaux?: string;

  createdAt: string;
  updatedAt: string;

  // Additional fields from the backend
  isExpired?: boolean;
  daysRemaining?: number;
  formattedDeadline?: string;

  // Additional fields that might not be in the database but we'll parse from existing fields
  financial_benefits_list?: string[];
  eligibility_criteria_list?: string[];
  study_fields?: string[];
  universities?: string[];
  required_documents?: { name: string; description: string }[];
  deadline_description?: string;
}

// Loading skeleton component
const LoadingSkeleton: React.FC = () => (
  <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 animate-pulse">
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <div className="h-96 bg-gray-200 rounded-xl"></div>
      <div className="lg:col-span-2 space-y-6">
        <div className="h-8 bg-gray-200 rounded w-1/3"></div>
        <div className="h-4 bg-gray-200 rounded w-full"></div>
        <div className="h-4 bg-gray-200 rounded w-full"></div>
        <div className="h-4 bg-gray-200 rounded w-5/6"></div>

        <div className="h-8 bg-gray-200 rounded w-1/3 mt-8"></div>
        <div className="h-4 bg-gray-200 rounded w-full"></div>
        <div className="h-4 bg-gray-200 rounded w-full"></div>
      </div>
    </div>
  </div>
);

const EnhancedScholarshipDetailPage: React.FC = () => {
  const { id, slug } = useParams<{ id?: string; slug?: string }>();
  const navigate = useNavigate();
  const [scholarship, setScholarship] = useState<Scholarship | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchScholarship = async () => {
      setLoading(true);
      setError(null);

      try {
        let scholarshipId: string | null = null;

        // If we have an ID, use it directly
        if (id) {
          scholarshipId = id;
        }
        // If we have a slug, extract the ID from it
        else if (slug) {
          const extractedId = extractIdFromSlug(slug);
          if (extractedId) {
            scholarshipId = extractedId.toString();
          } else {
            throw new Error('Invalid slug format');
          }
        }

        if (scholarshipId) {
          // Log the request for debugging
          console.log(`Fetching scholarship with ID ${scholarshipId}`);

          try {
            // Log the URL we're fetching from
            console.log(`Fetching from URL: /api/scholarships/${scholarshipId}`);

            // Make the API request using axios directly to avoid any interceptors
            const response = await fetch(`http://localhost:5000/api/scholarships/${scholarshipId}`);

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('API Response:', data);

            // Handle the correct API response format: { success: true, data: {...} }
            const scholarshipData = data.data || data;

            // Process the scholarship data
            if (scholarshipData) {
              processScholarshipData(scholarshipData);
            } else {
              console.error('Unexpected API response format:', data);
              throw new Error('Invalid API response format');
            }
          } catch (apiError) {
            // This will be caught by the outer try/catch block
            throw apiError;
          }
        } else {
          throw new Error('No valid ID or slug provided');
        }
      } catch (err: any) {
        console.error('Error fetching scholarship:', err);

        // Provide more detailed error message if available
        if (err.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          const statusCode = err.response.status;
          const errorMessage = err.response.data?.message || 'An error occurred';

          if (statusCode === 404) {
            setError(`La bourse avec l'ID ${id || slug} n'existe pas. Veuillez vérifier l'ID et réessayer.`);
          } else {
            setError(`Erreur ${statusCode}: ${errorMessage}`);
          }
        } else if (err.request) {
          // The request was made but no response was received
          setError('Aucune réponse reçue du serveur. Veuillez vérifier votre connexion et que le serveur backend est en cours d\'exécution.');
          console.log('Request that failed:', err.request);
        } else {
          // Something happened in setting up the request that triggered an Error
          setError(`Échec du chargement des détails de la bourse: ${err.message}`);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchScholarship();
  }, [id, slug]);

  // Process and enhance the scholarship data
  const processScholarshipData = (data: Scholarship) => {
    // Ensure we have valid date strings
    const deadline = typeof data.deadline === 'object'
      ? new Date(data.deadline).toISOString()
      : data.deadline;

    const createdAt = data.createdAt || new Date().toISOString();
    const updatedAt = data.updatedAt || new Date().toISOString();

    // Calculate date-related fields if not provided by the API
    const isExpired = data.isExpired !== undefined
      ? data.isExpired
      : dateUtils.isDatePast(deadline);

    const daysRemaining = data.daysRemaining !== undefined
      ? data.daysRemaining
      : dateUtils.getDaysRemaining(deadline);

    const formattedDeadline = data.formattedDeadline || dateUtils.formatDate(deadline, DateFormat.MEDIUM);

    // Parse string fields into arrays where needed
    const enhancedData = {
      ...data,
      deadline,
      createdAt,
      updatedAt,
      isExpired,
      daysRemaining,
      formattedDeadline,

      // Parse financial benefits into a list if it exists
      financial_benefits_list: data.financial_benefits_summary
        ? data.financial_benefits_summary.split(',').map(item => item.trim())
        : [],

      // Parse eligibility criteria into a list if it exists
      eligibility_criteria_list: data.eligibility_summary
        ? data.eligibility_summary.split(',').map(item => item.trim())
        : [],

      // These fields might not exist in the database, so we create placeholders
      // In a real implementation, these would come from the database or be parsed from other fields
      study_fields: data.study_fields || ['Business', 'Engineering', 'Computer Science', 'Medicine'],
      universities: data.universities || ['University of Paris', 'Sorbonne University', 'École Polytechnique'],
      required_documents: data.required_documents || [
        { name: 'CV/Resume', description: 'Updated curriculum vitae' },
        { name: 'Motivation Letter', description: 'Explaining why you deserve this scholarship' },
        { name: 'Academic Transcripts', description: 'Official transcripts from your institution' }
      ],
      deadline_description: data.deadline_description || 'Applications must be submitted before midnight on the deadline date.'
    };

    // Log the processed data for debugging
    console.log('Processed scholarship data:', enhancedData);

    setScholarship(enhancedData);
  };

  // Render loading state
  if (loading) {
    return <LoadingSkeleton />;
  }

  // Render error state
  if (error || !scholarship) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <Alert
          message="Bourse non trouvée"
          description={error || "La bourse que vous recherchez n'existe pas ou a été supprimée."}
          type="error"
          showIcon
          className="mb-6"
        />
        <div className="mt-6">
          <Link
            to="/scholarships"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Retour aux bourses
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* SEO Metadata */}
      <Helmet>
        <title>{scholarship.title} | MaBourse</title>
        <meta name="description" content={`${scholarship.title} - ${scholarship.description.substring(0, 160)}...`} />
        <meta property="og:title" content={`${scholarship.title} | MaBourse`} />
        <meta property="og:description" content={`${scholarship.title} - ${scholarship.description.substring(0, 160)}...`} />
        {scholarship.thumbnail && <meta property="og:image" content={scholarship.thumbnail} />}
        <script type="application/ld+json">
          {JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'EducationalOccupationalCredential',
            'name': scholarship.title,
            'description': scholarship.description,
            'educationalLevel': scholarship.level,
            'validIn': {
              '@type': 'Country',
              'name': scholarship.country
            },
            'validUntil': scholarship.deadline
          })}
        </script>
      </Helmet>

      {/* Professional Hero Section - Matching Home Page Style */}
      <section className="relative overflow-hidden bg-gradient-to-br from-gray-900 via-primary-dark to-primary mt-16">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}></div>
        </div>

        {/* Decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-12 -right-12 w-48 h-48 rounded-full bg-white/5 blur-3xl"></div>
          <div className="absolute top-1/2 -left-12 w-40 h-40 rounded-full bg-white/5 blur-3xl"></div>
        </div>

        {/* Hero content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-2xl sm:text-3xl font-semibold text-white leading-tight">
              {scholarship.title}
            </h1>
          </div>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-5">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-5">
            {/* Key Information Section - Simple List */}
            <section className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
              <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                <FileTextOutlined className="text-primary mr-2" />
                Informations clés
              </h2>
              <ul className="text-sm space-y-1.5">
                {scholarship.deadline && (
                  <li className="flex items-start">
                    <CalendarOutlined className="text-primary mr-2 mt-0.5" />
                    <div>
                      <div className="flex items-center">
                        <span className="font-medium text-gray-700">Date limite :</span>
                        <span className="text-gray-600 ml-1">
                          {scholarship.formattedDeadline || dateUtils.formatDate(scholarship.deadline, DateFormat.MEDIUM)}
                        </span>

                        {/* Status badge */}
                        {scholarship.isExpired ? (
                          <Badge
                            className="ml-2"
                            count="Expirée"
                            style={{ backgroundColor: '#ff4d4f' }}
                          />
                        ) : (
                          <Badge
                            className="ml-2"
                            count={`${scholarship.daysRemaining || 0} jour${(scholarship.daysRemaining || 0) !== 1 ? 's' : ''} restant${(scholarship.daysRemaining || 0) !== 1 ? 's' : ''}`}
                            style={{ backgroundColor: (scholarship.daysRemaining || 0) <= 7 ? '#faad14' : '#52c41a' }}
                          />
                        )}
                      </div>

                      {/* Days remaining indicator */}
                      {!scholarship.isExpired && (
                        <div className="text-xs text-gray-500 mt-1 flex items-center">
                          <ClockCircleOutlined className="mr-1" />
                          {(scholarship.daysRemaining || 0) === 0
                            ? "Dernier jour pour postuler !"
                            : `Il reste ${scholarship.daysRemaining || 0} jour${(scholarship.daysRemaining || 0) !== 1 ? 's' : ''} pour postuler`}
                        </div>
                      )}
                    </div>
                  </li>
                )}

                {scholarship.level && (
                  <li className="flex items-center">
                    <UserOutlined className="text-primary mr-2" />
                    <span className="font-medium text-gray-700">Niveau d'études :</span>
                    <span className="text-gray-600 ml-1">{scholarship.level}</span>
                  </li>
                )}

                {scholarship.country && (
                  <li className="flex items-center">
                    <GlobalOutlined className="text-primary mr-2" />
                    <span className="font-medium text-gray-700">Pays :</span>
                    <span className="text-gray-600 ml-1">{scholarship.country}</span>
                  </li>
                )}

                {scholarship.coverage && (
                  <li className="flex items-center">
                    <TrophyOutlined className="text-primary mr-2" />
                    <span className="font-medium text-gray-700">Couverture :</span>
                    <span className="text-gray-600 ml-1">{scholarship.coverage}</span>
                  </li>
                )}
              </ul>
            </section>

            {/* 1. Description Section */}
            <section className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
              <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                <FileTextOutlined className="text-primary mr-2" />
                Description
              </h2>
              <div className="prose max-w-none text-gray-600 text-sm leading-relaxed">
                <p className="whitespace-pre-line">{scholarship.description}</p>
              </div>
            </section>

            {/* 2. Domaines d'Études Section */}
            {scholarship.domainesEtudes && (
              <section className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
                <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                  <BookOutlined className="text-primary mr-2" />
                  Domaines d'Études
                </h2>
                <div className="prose max-w-none text-gray-600 text-sm leading-relaxed">
                  <p className="whitespace-pre-line">{scholarship.domainesEtudes}</p>
                </div>
              </section>
            )}

            {/* 3. Universités Participantes Section */}
            {scholarship.universitesParticipantes && (
              <section className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
                <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                  <BankOutlined className="text-primary mr-2" />
                  Universités Participantes
                </h2>
                <div className="prose max-w-none text-gray-600 text-sm leading-relaxed">
                  <p className="whitespace-pre-line">{scholarship.universitesParticipantes}</p>
                </div>
              </section>
            )}

            {/* 4. Documents Requis Section */}
            {scholarship.documentsRequis && (
              <section className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
                <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                  <FileTextOutlined className="text-primary mr-2" />
                  Documents Requis
                </h2>
                <div className="prose max-w-none text-gray-600 text-sm leading-relaxed">
                  <p className="whitespace-pre-line">{scholarship.documentsRequis}</p>
                </div>
              </section>
            )}

            {/* 5. Conditions d'Éligibilité Section */}
            {scholarship.conditionsEligibilite && (
              <section className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
                <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                  <CheckCircleOutlined className="text-primary mr-2" />
                  Conditions d'Éligibilité
                </h2>
                <div className="prose max-w-none text-gray-600 text-sm leading-relaxed">
                  <p className="whitespace-pre-line">{scholarship.conditionsEligibilite}</p>
                </div>
              </section>
            )}

            {/* 6. Lien pour Postuler Section */}
            {scholarship.lienPostuler && (
              <section className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
                <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                  <LinkOutlined className="text-primary mr-2" />
                  Lien pour Postuler
                </h2>
                <div className="prose max-w-none text-gray-600 text-sm leading-relaxed">
                  <p className="whitespace-pre-line">{scholarship.lienPostuler}</p>
                  {/* If the content contains a URL, make it clickable */}
                  {scholarship.lienPostuler.includes('http') && (
                    <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <a
                        href={scholarship.lienPostuler.match(/https?:\/\/[^\s]+/)?.[0] || scholarship.lienPostuler}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200 text-sm font-medium"
                      >
                        <LinkOutlined className="mr-2" />
                        Postuler Maintenant
                      </a>
                    </div>
                  )}
                </div>
              </section>
            )}

            {/* 7. Chaîne YouTube / Réseaux Sociaux Section */}
            {scholarship.chaineYoutubeReseaux && (
              <section className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
                <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                  <YoutubeOutlined className="text-red-600 mr-2" />
                  Chaîne YouTube / Réseaux Sociaux
                </h2>
                <div className="prose max-w-none text-gray-600 text-sm leading-relaxed">
                  <p className="whitespace-pre-line">{scholarship.chaineYoutubeReseaux}</p>
                  {/* Extract and display social media links if they exist */}
                  {scholarship.chaineYoutubeReseaux.includes('http') && (
                    <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-3">
                      {scholarship.chaineYoutubeReseaux.match(/https?:\/\/[^\s]+/g)?.map((url, index) => (
                        <a
                          key={index}
                          href={url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center px-3 py-2 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors duration-200 text-xs"
                        >
                          {url.includes('youtube') && <YoutubeOutlined className="mr-2 text-red-600" />}
                          {url.includes('facebook') && <span className="mr-2 text-blue-600">📘</span>}
                          {url.includes('instagram') && <span className="mr-2 text-pink-600">📷</span>}
                          {url.includes('linkedin') && <span className="mr-2 text-blue-700">💼</span>}
                          {url.includes('telegram') && <span className="mr-2 text-blue-500">✈️</span>}
                          <span className="truncate">{url.replace(/https?:\/\//, '').substring(0, 30)}...</span>
                        </a>
                      ))}
                    </div>
                  )}
                </div>
              </section>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-4">
            {/* Newsletter Subscription */}
            <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
              <h3 className="text-base font-bold text-gray-900 mb-2">Restez informé</h3>
              <p className="text-xs text-gray-600 mb-3">
                Recevez les dernières bourses et opportunités directement dans votre boîte mail.
              </p>
              <form className="space-y-2">
                <div>
                  <input
                    type="email"
                    placeholder="Votre adresse email"
                    className="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>
                <Button
                  type="primary"
                  size="small"
                  className="w-full"
                >
                  S'abonner
                </Button>
                <p className="text-xs text-gray-500 mt-1">
                  En vous inscrivant, vous acceptez notre politique de confidentialité.
                </p>
              </form>
            </div>

            {/* Enhanced Suggested Scholarships */}
            <div className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold text-gray-900">Bourses Similaires</h3>
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                  <BookOutlined className="text-primary text-sm" />
                </div>
              </div>

              <div className="space-y-3">
                {/* Enhanced scholarship cards */}
                {[
                  { id: 6, title: "Fulbright Scholarship Program", country: "United States", level: "Graduate", image: 1 },
                  { id: 7, title: "Chevening Scholarship", country: "United Kingdom", level: "Master's", image: 2 },
                  { id: 8, title: "DAAD Scholarship", country: "Germany", level: "PhD", image: 3 },
                  { id: 10, title: "Bourse du gouvernement indien", country: "Inde", level: "Master's", image: 1 },
                  { id: 11, title: "Erasmus+ Programme", country: "Europe", level: "Graduate", image: 2 }
                ].map((scholarshipItem, index) => (
                  <div
                    key={index}
                    className="group flex items-start space-x-3 hover:bg-gray-50 rounded-lg p-2 transition-colors duration-200 cursor-pointer"
                    onClick={() => navigate(`/scholarships/${scholarshipItem.id}`)}
                  >
                    {/* Enhanced image - Made bigger for better poster display */}
                    <div className="flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden">
                      <img
                        src={`/assets/scholarship${scholarshipItem.image}.jpg`}
                        alt="Scholarship thumbnail"
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = `https://images.unsplash.com/photo-${1523050854058 + index}?ixlib=rb-4.0.3&w=400&h=400&fit=crop&crop=center`;
                        }}
                      />
                    </div>

                    {/* Content - Only title and deadline */}
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 group-hover:text-primary transition-colors duration-200 line-clamp-2 leading-tight mb-1">
                        {scholarshipItem.title}
                      </h4>

                      {/* Only deadline */}
                      <div className="flex items-center">
                        <CalendarOutlined className="text-gray-400 text-xs mr-1" />
                        <span className="text-xs text-gray-500">
                          {new Date(new Date().setMonth(new Date().getMonth() + index + 1)).toLocaleDateString('fr-FR')}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}

                {/* Enhanced action buttons */}
                <div className="pt-3 border-t border-gray-100">
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      type="default"
                      size="small"
                      className="flex items-center justify-center"
                      onClick={() => navigate('/scholarships')}
                    >
                      <BookOutlined className="mr-1" />
                      Voir plus
                    </Button>
                    <Button
                      type="primary"
                      size="small"
                      className="flex items-center justify-center"
                      onClick={() => navigate('/scholarships')}
                    >
                      <GlobalOutlined className="mr-1" />
                      Explorer
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Contact and Social Media Section */}
        <div className="mt-16 bg-gradient-to-r from-primary/5 to-primary-dark/5 rounded-2xl p-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

            {/* Contact Section */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                  <svg className="w-5 h-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900">Contactez-Nous</h3>
              </div>

              <p className="text-sm text-gray-600 mb-4">
                Besoin d'aide pour votre candidature ? Notre équipe est là pour vous accompagner.
              </p>

              <div className="space-y-3">
                <div className="flex items-center text-sm text-gray-700">
                  <svg className="w-4 h-4 text-primary mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center text-sm text-gray-700">
                  <svg className="w-4 h-4 text-primary mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                  <span>+33 1 23 45 67 89</span>
                </div>
              </div>

              <Link
                to="/contact"
                className="inline-flex items-center mt-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200 text-sm font-medium"
              >
                <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                Nous Contacter
              </Link>
            </div>

            {/* Social Media Section */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                  <svg className="w-5 h-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V3a1 1 0 011 1v8.5a1 1 0 01-1 1h-2.5M7 4V3a1 1 0 00-1 1v8.5a1 1 0 001 1H9.5M7 4H5a1 1 0 00-1 1v10a1 1 0 001 1h2M9.5 12.5L11 14l4-4" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900">Suivez-Nous</h3>
              </div>

              <p className="text-sm text-gray-600 mb-4">
                Restez informé des dernières bourses et conseils pour réussir vos candidatures.
              </p>

              <div className="grid grid-cols-2 gap-3">
                {[
                  { name: 'Facebook', icon: '📘', url: 'https://facebook.com/mabourse', color: 'bg-blue-50 hover:bg-blue-100 text-blue-700' },
                  { name: 'YouTube', icon: '📺', url: 'https://youtube.com/@mabourse', color: 'bg-red-50 hover:bg-red-100 text-red-700' },
                  { name: 'Instagram', icon: '📷', url: 'https://instagram.com/mabourse', color: 'bg-pink-50 hover:bg-pink-100 text-pink-700' },
                  { name: 'Telegram', icon: '✈️', url: 'https://t.me/mabourse', color: 'bg-blue-50 hover:bg-blue-100 text-blue-600' }
                ].map((social, index) => (
                  <a
                    key={index}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`flex items-center p-3 rounded-lg transition-colors duration-200 ${social.color}`}
                  >
                    <span className="text-lg mr-2">{social.icon}</span>
                    <span className="text-sm font-medium">{social.name}</span>
                  </a>
                ))}
              </div>

              <div className="mt-4 p-3 bg-gradient-to-r from-primary/10 to-primary-dark/10 rounded-lg">
                <p className="text-xs text-gray-600 text-center">
                  🔔 Activez les notifications pour ne manquer aucune opportunité !
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default EnhancedScholarshipDetailPage;
