import React from 'react';
import StandardOpportunityPage from '../components/StandardOpportunityPage';

const InternshipPage: React.FC = () => {
  const pageConfig = {
    type: 'internship',
    title: 'Stages Professionnels - Lancez Votre Carrière | MaBourse',
    description: 'Trouvez le stage idéal pour démarrer votre carrière. Découvrez des opportunités de stages dans les meilleures entreprises et organisations.',
    keywords: 'stages professionnels, stage étudiant, stage entreprise, expérience professionnelle, stage rémunéré',
    
    heroTitle: 'Stages Professionnels',
    heroDescription: 'Lancez votre carrière avec des stages de qualité dans les meilleures entreprises. Acquérez une expérience professionnelle précieuse et construisez votre réseau professionnel.',
    
    infoTitle: 'Pourquoi Faire un Stage Professionnel ?',
    infoContent: 'Le stage professionnel est une étape cruciale dans votre parcours académique et professionnel. Il vous permet de mettre en pratique vos connaissances théoriques, de découvrir le monde du travail et de vous faire remarquer par de futurs employeurs.',
    benefits: [
      'Expérience pratique dans votre domaine d\'études',
      'Développement de compétences professionnelles essentielles',
      'Construction d\'un réseau professionnel solide',
      'Possibilité d\'embauche à l\'issue du stage',
      'Amélioration significative de votre CV',
      'Découverte de la culture d\'entreprise et des métiers'
    ],
    apiEndpoint: '/api/opportunities/search'
  };

  return <StandardOpportunityPage config={pageConfig} />;
};

export default InternshipPage;
