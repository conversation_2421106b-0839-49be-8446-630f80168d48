import React from 'react';
import StandardOpportunityPage from '../components/StandardOpportunityPage';

const InternshipPage: React.FC = () => {
  const pageConfig = {
    type: 'internship',
    title: 'Stages Professionnels - Lancez Votre Carrière | MaBourse',
    description: 'Trouvez le stage idéal pour démarrer votre carrière. Découvrez des opportunités de stages dans les meilleures entreprises et organisations.',
    keywords: 'stages professionnels, stage étudiant, stage entreprise, expérience professionnelle, stage rémunéré',

    heroTitle: 'Stages Professionnels',
    heroDescription: 'Découvrez notre collection exclusive de stages professionnels. Ces opportunités exceptionnelles sont proposées par des entreprises prestigieuses, des organisations internationales, et des institutions dédiées à l\'excellence professionnelle.',

    infoTitle: 'Pourquoi Faire un Stage Professionnel ?',
    infoContent: 'Que vous visiez l\'industrie technologique, le secteur financier, les organisations non gouvernementales, ou les entreprises innovantes, chaque stage offre une expérience pratique adaptée à vos ambitions professionnelles. Explorez, comparez et trouvez le stage qui transformera votre parcours de carrière.',
    benefits: [
      'Expérience pratique dans votre domaine d\'études',
      'Développement de compétences professionnelles essentielles',
      'Construction d\'un réseau professionnel solide',
      'Possibilité d\'embauche à l\'issue du stage',
      'Amélioration significative de votre CV',
      'Découverte de la culture d\'entreprise et des métiers'
    ],
    apiEndpoint: '/api/opportunities/search'
  };

  return <StandardOpportunityPage config={pageConfig} />;
};

export default InternshipPage;
