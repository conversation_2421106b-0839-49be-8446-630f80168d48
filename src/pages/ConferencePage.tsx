import React from 'react';
import StandardOpportunityPage from '../components/StandardOpportunityPage';

const ConferencePage: React.FC = () => {
  const pageConfig = {
    type: 'conference',
    title: 'Conférences Professionnelles - Élargissez Vos Horizons | MaBourse',
    description: 'Participez aux meilleures conférences professionnelles. Restez à la pointe de votre domaine et élargissez votre réseau professionnel.',
    keywords: 'conférences professionnelles, événements académiques, networking, développement professionnel, conférences internationales',
    
    heroTitle: 'Conférences Professionnelles',
    heroDescription: 'Participez aux conférences les plus prestigieuses de votre domaine. Restez à la pointe des innovations, échangez avec des experts et élargissez votre réseau professionnel international.',
    
    infoTitle: 'Pourquoi Participer à des Conférences ?',
    infoContent: 'Les conférences professionnelles sont des événements incontournables pour rester à jour dans votre domaine d\'expertise. Elles offrent une opportunité unique d\'apprendre des meilleurs experts, de découvrir les dernières innovations et de créer des connexions professionnelles durables.',
    benefits: [
      'Accès aux dernières recherches et innovations de votre secteur',
      'Opportunités de networking avec des experts internationaux',
      'Présentation de vos propres travaux et recherches',
      'Développement de votre visibilité professionnelle',
      'Formation continue et mise à jour de vos connaissances',
      'Inspiration et nouvelles perspectives pour vos projets'
    ],
    apiEndpoint: '/api/opportunities/search'
  };

  return <StandardOpportunityPage config={pageConfig} />;
};

export default ConferencePage;
