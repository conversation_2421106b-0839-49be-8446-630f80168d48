import React from 'react';
import StandardOpportunityPage from '../components/StandardOpportunityPage';

const ConferencePage: React.FC = () => {
  const pageConfig = {
    type: 'conference',
    title: 'Conférences Professionnelles - Élargissez Vos Horizons | MaBourse',
    description: 'Participez aux meilleures conférences professionnelles. Restez à la pointe de votre domaine et élargissez votre réseau professionnel.',
    keywords: 'conférences professionnelles, événements académiques, networking, développement professionnel, conférences internationales',
    
    heroTitle: 'Conférences Professionnelles',
    heroDescription: 'Découvrez notre collection exclusive de conférences professionnelles. Ces opportunités exceptionnelles sont proposées par des organisations prestigieuses, des universités de renommée mondiale, et des institutions dédiées à l\'excellence académique.',

    infoTitle: 'Pourquoi Participer à des Conférences ?',
    infoContent: 'Que vous visiez les conférences technologiques, les événements académiques, les symposiums internationaux, ou les forums d\'innovation, chaque conférence offre un apprentissage enrichissant adapté à vos ambitions professionnelles. Explorez, comparez et trouvez la conférence qui transformera votre parcours de carrière.',
    benefits: [
      'Accès aux dernières recherches et innovations de votre secteur',
      'Opportunités de networking avec des experts internationaux',
      'Présentation de vos propres travaux et recherches',
      'Développement de votre visibilité professionnelle',
      'Formation continue et mise à jour de vos connaissances',
      'Inspiration et nouvelles perspectives pour vos projets'
    ],
    apiEndpoint: '/api/opportunities/search'
  };

  return <StandardOpportunityPage config={pageConfig} />;
};

export default ConferencePage;
