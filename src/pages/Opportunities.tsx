import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';

interface Opportunity {
  id: number;
  title: string;
  description: string;
  type: 'internship' | 'training' | 'conference' | 'workshop' | 'competition';
  organization: string;
  location: string;
  isRemote: boolean;
  deadline: string;
  startDate?: string;
  endDate?: string;
  applicationLink?: string;
  thumbnail?: string;
  isActive: boolean;
  tags?: string[];
}

const Opportunities: React.FC = () => {
  const { translations, direction } = useLanguage();
  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    type: '',
    location: '',
    isRemote: ''
  });
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchOpportunities();
  }, [filters]);

  const fetchOpportunities = async () => {
    try {
      const params = new URLSearchParams({
        active: 'true',
        orderBy: 'deadline',
        orderDirection: 'ASC',
        ...filters
      });

      const response = await fetch(`/api/opportunities?${params}`);
      if (response.ok) {
        const data = await response.json();
        setOpportunities(data.data.opportunities || []);
      } else {
        console.error('Failed to fetch opportunities');
      }
    } catch (error) {
      console.error('Error fetching opportunities:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (searchTerm.trim() === '') {
      fetchOpportunities();
      return;
    }

    try {
      const params = new URLSearchParams({
        q: searchTerm,
        ...filters
      });

      const response = await fetch(`/api/opportunities/search?${params}`);
      if (response.ok) {
        const data = await response.json();
        setOpportunities(data.data.opportunities || []);
      }
    } catch (error) {
      console.error('Error searching opportunities:', error);
    }
  };

  const getTypeIcon = (type: string): string => {
    const icons = {
      internship: '🎓',
      training: '📚',
      conference: '🎤',
      workshop: '🔧',
      competition: '🏆'
    };
    return icons[type as keyof typeof icons] || '📋';
  };

  const getTypeColor = (type: string): string => {
    const colors = {
      internship: 'bg-blue-100 text-blue-800',
      training: 'bg-green-100 text-green-800',
      conference: 'bg-purple-100 text-purple-800',
      workshop: 'bg-orange-100 text-orange-800',
      competition: 'bg-red-100 text-red-800'
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const isExpired = (deadline: string): boolean => {
    return new Date(deadline) < new Date();
  };

  const getDaysUntilDeadline = (deadline: string): number => {
    const today = new Date();
    const deadlineDate = new Date(deadline);
    const diffTime = deadlineDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Chargement des opportunités...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section - Standard Professional Design */}
      <div className="relative bg-gradient-to-br from-primary via-primary/90 to-secondary overflow-hidden">
        {/* Background overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-primary-dark/20 to-secondary/20 mix-blend-multiply" />

        {/* Background pattern */}
        <div className="absolute inset-0 opacity-10">
          <svg className="h-full w-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="opportunities-grid" width="20" height="20" patternUnits="userSpaceOnUse">
                <circle cx="10" cy="10" r="1" fill="white" opacity="0.3" />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#opportunities-grid)" />
          </svg>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-32 h-32 bg-secondary/20 rounded-full blur-2xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-20 left-1/4 w-16 h-16 bg-white/10 rounded-full blur-lg animate-pulse delay-500"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-24 pb-16 sm:pt-28 sm:pb-20 relative z-10">
          <div className="text-center">
            <div className="inline-flex items-center px-3 py-1.5 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white/90 text-xs sm:text-sm font-medium mb-4">
              <svg className={`w-3 h-3 sm:w-4 sm:h-4 ${direction === 'rtl' ? 'ml-2' : 'mr-2'}`} fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Opportunités Professionnelles
            </div>
            <h1 className="text-3xl tracking-tight font-bold text-white sm:text-4xl lg:text-5xl animate-fade-in mb-4">
              {translations.opportunities.title}
            </h1>
            <p className="mt-3 max-w-2xl mx-auto text-base text-white/85 sm:text-lg lg:text-xl animate-slide-up leading-relaxed">
              {translations.opportunities.subtitle}
            </p>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-10 relative z-10">
        <div className="bg-white rounded-xl shadow-lg p-6 sm:p-8 border border-gray-100">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 sm:gap-6 mb-6">
            <div className="md:col-span-2">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Rechercher des opportunités..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className={`w-full px-4 py-3 ${direction === 'rtl' ? 'pr-12 pl-4' : 'pl-12 pr-4'} border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors duration-200`}
                />
                <div className={`absolute inset-y-0 ${direction === 'rtl' ? 'right-0 pr-3' : 'left-0 pl-3'} flex items-center pointer-events-none`}>
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </div>
            
            <div>
              <select
                value={filters.type}
                onChange={(e) => setFilters({ ...filters, type: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors duration-200"
              >
                <option value="">{translations.opportunities.filters.all}</option>
                <option value="internship">{translations.opportunities.types.internship}</option>
                <option value="training">{translations.opportunities.types.training}</option>
                <option value="conference">{translations.opportunities.types.conference}</option>
                <option value="workshop">{translations.opportunities.types.workshop}</option>
                <option value="competition">{translations.opportunities.types.competition}</option>
              </select>
            </div>

            <div>
              <select
                value={filters.isRemote}
                onChange={(e) => setFilters({ ...filters, isRemote: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors duration-200"
              >
                <option value="">Présentiel et distanciel</option>
                <option value="true">Distanciel uniquement</option>
                <option value="false">Présentiel uniquement</option>
              </select>
            </div>
          </div>
          
          <div className="flex justify-center">
            <button
              onClick={handleSearch}
              className="px-8 py-3 bg-primary text-white font-semibold rounded-lg hover:bg-primary-dark transition-colors duration-300 shadow-sm hover:shadow-md"
            >
              Rechercher
            </button>
          </div>
        </div>
      </div>

      {/* Type Quick Filters */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4">
          {Object.entries(translations.opportunities.types).map(([key, label]) => (
            <button
              key={key}
              onClick={() => setFilters({ ...filters, type: filters.type === key ? '' : key })}
              className={`p-4 sm:p-6 rounded-xl border transition-all duration-200 ${
                filters.type === key
                  ? 'border-primary bg-primary/5 text-primary shadow-sm'
                  : 'border-gray-200 bg-white hover:border-primary/30 hover:bg-primary/5 hover:shadow-sm'
              }`}
            >
              <div className="text-2xl sm:text-3xl mb-2">{getTypeIcon(key)}</div>
              <div className="font-medium text-sm sm:text-base">{label}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Opportunities Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        {opportunities.length === 0 ? (
          <div className="text-center py-16">
            <div className="text-6xl mb-6">🔍</div>
            <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3">
              Aucune opportunité trouvée
            </h3>
            <p className="text-gray-600 max-w-md mx-auto">
              Essayez de modifier vos filtres ou votre recherche pour découvrir plus d'opportunités.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
            {opportunities.map((opportunity) => {
              const expired = isExpired(opportunity.deadline);
              const daysLeft = getDaysUntilDeadline(opportunity.deadline);
              
              return (
                <div
                  key={opportunity.id}
                  className={`bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-gray-100 ${
                    expired ? 'opacity-75' : 'hover:border-primary/20 hover:-translate-y-1'
                  }`}
                >
                  {opportunity.thumbnail && (
                    <div className="relative overflow-hidden">
                      <img
                        src={opportunity.thumbnail}
                        alt={opportunity.title}
                        className="w-full h-40 sm:h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                  )}
                  
                  <div className="p-4 sm:p-6">
                    <div className="flex items-center justify-between mb-3">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(opportunity.type)}`}>
                        <span className={`${direction === 'rtl' ? 'ml-1' : 'mr-1'}`}>{getTypeIcon(opportunity.type)}</span>
                        {translations.opportunities.types[opportunity.type]}
                      </span>

                      {opportunity.isRemote && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          🌐 Distanciel
                        </span>
                      )}
                    </div>

                    <h3 className={`text-lg font-semibold text-gray-900 mb-2 line-clamp-2 ${direction === 'rtl' ? 'text-right' : 'text-left'}`}>
                      {opportunity.title}
                    </h3>

                    <p className={`text-gray-600 text-sm mb-3 line-clamp-3 leading-relaxed ${direction === 'rtl' ? 'text-right' : 'text-left'}`}>
                      {opportunity.description}
                    </p>
                    
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-sm text-gray-600">
                        <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                        {opportunity.organization}
                      </div>
                      
                      <div className="flex items-center text-sm text-gray-600">
                        <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        {opportunity.location}
                      </div>
                      
                      <div className={`flex items-center text-sm ${expired ? 'text-red-600' : daysLeft <= 7 ? 'text-orange-600' : 'text-gray-600'}`}>
                        <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        {expired ? (
                          <span className="font-medium">{translations.opportunities.expired}</span>
                        ) : (
                          <span>
                            Date limite: {formatDate(opportunity.deadline)}
                            {daysLeft <= 7 && (
                              <span className="ml-1 font-medium">
                                ({daysLeft} jour{daysLeft !== 1 ? 's' : ''} restant{daysLeft !== 1 ? 's' : ''})
                              </span>
                            )}
                          </span>
                        )}
                      </div>
                    </div>
                    
                    {opportunity.tags && opportunity.tags.length > 0 && (
                      <div className="mb-4 flex flex-wrap gap-1">
                        {opportunity.tags.slice(0, 3).map((tag, index) => (
                          <span
                            key={index}
                            className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded"
                          >
                            #{tag}
                          </span>
                        ))}
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between">
                      <Link
                        to={`/opportunities/${opportunity.id}`}
                        className="text-primary text-sm font-medium hover:text-primary-light transition-colors duration-200"
                      >
                        {translations.opportunities.viewDetails}
                      </Link>
                      
                      {opportunity.applicationLink && !expired && (
                        <a
                          href={opportunity.applicationLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="px-4 py-2 bg-gradient-to-r from-primary to-primary-light text-white text-sm font-medium rounded-lg hover:from-primary-light hover:to-primary shadow-md hover:shadow-lg transition-all duration-200"
                        >
                          {translations.opportunities.apply}
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default Opportunities;
