import React from 'react';
import StandardOpportunityPage from '../components/StandardOpportunityPage';

const CompetitionPage: React.FC = () => {
  const pageConfig = {
    type: 'competition',
    title: 'Concours et Compétitions - Défiez-vous et Excellez | MaBourse',
    description: 'Participez aux meilleurs concours et compétitions académiques et professionnels. Défiez-vous, excellez et gagnez des prix prestigieux.',
    keywords: 'concours académiques, compétitions professionnelles, prix étudiants, challenges, concours internationaux',
    
    heroTitle: 'Concours et Compétitions',
    heroDescription: 'Relevez des défis stimulants et mesurez-vous aux meilleurs. Participez à des concours prestigieux qui peuvent transformer votre parcours académique et professionnel.',
    
    infoTitle: 'Pourquoi Participer à des Concours ?',
    infoContent: 'Les concours et compétitions sont d\'excellentes opportunités pour tester vos compétences, gagner en visibilité et remporter des prix prestigieux. Ils vous permettent de vous démarquer dans votre domaine et d\'enrichir considérablement votre CV.',
    benefits: [
      'Reconnaissance de vos talents et compétences',
      'Opportunités de gains financiers et de bourses',
      'Visibilité auprès d\'employeurs et d\'institutions prestigieuses',
      'Développement de votre esprit de compétition',
      'Networking avec d\'autres talents de votre domaine',
      'Expérience enrichissante pour votre parcours professionnel'
    ],
    apiEndpoint: '/api/opportunities/search'
  };

  return <StandardOpportunityPage config={pageConfig} />;
};

export default CompetitionPage;
