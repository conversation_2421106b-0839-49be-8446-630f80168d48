import React from 'react';
import StandardOpportunityPage from '../components/StandardOpportunityPage';

const CompetitionPage: React.FC = () => {
  const pageConfig = {
    type: 'competition',
    title: 'Concours et Compétitions - Défiez-vous et Excellez | MaBourse',
    description: 'Participez aux meilleurs concours et compétitions académiques et professionnels. Défiez-vous, excellez et gagnez des prix prestigieux.',
    keywords: 'concours académiques, compétitions professionnelles, prix étudiants, challenges, concours internationaux',
    
    heroTitle: 'Concours et Compétitions',
    heroDescription: 'Découvrez notre collection exclusive de concours et compétitions. Ces opportunités exceptionnelles sont proposées par des organisations prestigieuses, des universités de renommée mondiale, et des institutions dédiées à l\'excellence académique.',

    infoTitle: 'Pourquoi Participer à des Concours ?',
    infoContent: 'Que vous visiez les concours technologiques, les compétitions académiques, les challenges d\'innovation, ou les prix d\'excellence, chaque concours offre une opportunité de reconnaissance adaptée à vos ambitions professionnelles. Explorez, comparez et trouvez le concours qui transformera votre parcours de carrière.',
    benefits: [
      'Reconnaissance de vos talents et compétences',
      'Opportunités de gains financiers et de bourses',
      'Visibilité auprès d\'employeurs et d\'institutions prestigieuses',
      'Développement de votre esprit de compétition',
      'Networking avec d\'autres talents de votre domaine',
      'Expérience enrichissante pour votre parcours professionnel'
    ],
    apiEndpoint: '/api/opportunities/search'
  };

  return <StandardOpportunityPage config={pageConfig} />;
};

export default CompetitionPage;
