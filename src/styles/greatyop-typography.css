/* GreatYOP Typography - Global Styles */

/* Base typography matching GreatYOP */
body, input, select, textarea, button {
  color: #3d3d3d;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif;
  font-size: 17px;
  line-height: 30px;
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
  clear: both;
  line-height: 1.3;
  color: #3d3d3d;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif;
}

/* Specific heading sizes matching GreatYOP */
h1 {
  font-size: 24px;
  margin-bottom: 10px;
}

h2 {
  font-size: 20px;
  margin-bottom: 8px;
}

h3 {
  font-size: 18px;
  margin-bottom: 6px;
}

h4 {
  font-size: 16px;
  margin-bottom: 4px;
}

/* Paragraphs */
p {
  margin-bottom: 10px;
  color: #3d3d3d;
  font-size: 17px;
  line-height: 30px;
  text-align: justify;
}

/* Links */
a {
  color: #3c6c90;
  text-decoration: none;
}

a:hover {
  color: #2d5a7b;
}

/* Card titles */
.entry-title, .gy-post-card .entry-title {
  color: #353535;
  margin-bottom: 0;
  padding-bottom: 0;
  text-transform: capitalize;
  font-size: 19px;
  margin-bottom: 10px;
  font-weight: 600;
  line-height: 1.4;
}

/* Small text elements */
.entry-meta, .entry-meta span, .entry-meta span a {
  color: #767676;
  font-style: normal;
  font-size: 13px;
  display: inline-block;
  margin-right: 15px;
}

/* Button styling */
button, input[type="submit"], .btn, .button {
  background: #FF9C1A;
  color: #000;
  font-size: 14px;
  line-height: 14px;
  padding: 10px 15px;
  font-weight: 700;
  position: relative;
  text-shadow: none;
  border-radius: 5px;
  border: none;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif;
}

/* Input fields */
input[type="email"], input[type="search"], input[type="text"], textarea {
  color: #666;
  border: 1px solid #ccc;
  height: 36px;
  width: 100%;
  padding: 3px 6px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif;
  font-size: 15px;
}

/* Navigation and menu text */
.nav-links, .navigation, .menu {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif;
  font-size: 15px;
  color: #3d3d3d;
}

/* Hero section text */
.hero-title, .hero h1 {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif;
  color: #3d3d3d;
  font-weight: 600;
}

/* Card content text */
.card-text, .card-description {
  font-size: 15px;
  line-height: 1.5;
  color: #3d3d3d;
}

/* Footer text */
.footer-text, footer p {
  font-size: 14px;
  line-height: 1.4;
  color: #767676;
}

/* Newsletter text */
.newsletter h2, .newsletter-title {
  font-size: 20px;
  font-weight: 600;
  color: #3d3d3d;
}

.newsletter p, .newsletter-description {
  font-size: 15px;
  line-height: 1.5;
  color: #767676;
}

/* Sidebar text */
.sidebar, .widget {
  font-size: 15px;
  line-height: 1.5;
  color: #3d3d3d;
}

.widget-title {
  font-size: 18px;
  font-weight: 600;
  color: #3d3d3d;
  margin-bottom: 10px;
}

/* Pagination text */
.pagination, .page-numbers {
  font-size: 14px;
  font-weight: 500;
  color: #3d3d3d;
}

/* Breadcrumb text */
.breadcrumb, .breadcrumbs {
  font-size: 13px;
  color: #767676;
}

/* Form labels */
label {
  font-size: 14px;
  color: #3d3d3d;
  font-weight: 500;
}

/* Table text */
table, th, td {
  font-size: 15px;
  color: #3d3d3d;
}

/* Alert and notification text */
.alert, .notification, .message {
  font-size: 15px;
  line-height: 1.5;
}

/* Override Tailwind and other framework defaults */
.text-gray-900 {
  color: #3d3d3d !important;
}

.text-gray-700 {
  color: #3d3d3d !important;
}

.text-gray-600 {
  color: #3d3d3d !important; /* Changed from #767676 to match GreatYOP's darker text */
}

.text-gray-500 {
  color: #3d3d3d !important; /* Changed from gray to match GreatYOP */
}

.text-gray-400 {
  color: #767676 !important; /* Keep this for secondary/meta text */
}

.text-white {
  color: #ffffff !important;
}

/* Ensure all text elements use GreatYOP font family and colors */
* {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif;
}

/* Additional text color overrides for common classes */
.text-slate-600, .text-slate-700, .text-slate-800 {
  color: #3d3d3d !important;
}

.text-zinc-600, .text-zinc-700, .text-zinc-800 {
  color: #3d3d3d !important;
}

.text-neutral-600, .text-neutral-700, .text-neutral-800 {
  color: #3d3d3d !important;
}

/* Ensure paragraph text is dark */
p, span, div {
  color: #3d3d3d;
}

/* Override any remaining gray text */
.text-gray-800 {
  color: #3d3d3d !important;
}

/* Specific overrides for React components */
.react-component, .component {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif;
  color: #3d3d3d;
}

/* French text specific styling */
html[lang="fr"], html[lang="fr-FR"] {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif;
}

/* Ensure proper text rendering */
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Layout optimizations - Match GreatYOP's exact container system */

/* GreatYOP Container System - Match GreatYOP's exact margins */
.max-w-7xl, .max-w-6xl, .max-w-5xl, .max-w-4xl {
  max-width: 100% !important; /* Full width like GreatYOP */
  padding-left: 5% !important; /* Increased to match GreatYOP */
  padding-right: 5% !important; /* Increased to match GreatYOP */
  margin-left: auto !important;
  margin-right: auto !important;
}

/* GreatYOP responsive padding - matches their breakpoints */
@media (min-width: 1200px) {
  .max-w-7xl, .max-w-6xl, .max-w-5xl, .max-w-4xl {
    padding-left: 8% !important; /* Increased to match GreatYOP's wider margins */
    padding-right: 8% !important; /* Increased to match GreatYOP's wider margins */
  }
}

/* Override Tailwind padding classes to match GreatYOP */
.px-4, .px-6, .px-8 {
  padding-left: 5% !important; /* Increased to match GreatYOP */
  padding-right: 5% !important; /* Increased to match GreatYOP */
}

/* Responsive padding adjustments - GreatYOP style */
@media (min-width: 640px) {
  .sm\\:px-6 {
    padding-left: 5% !important; /* Increased to match GreatYOP */
    padding-right: 5% !important; /* Increased to match GreatYOP */
  }
}

@media (min-width: 1024px) {
  .lg\\:px-8 {
    padding-left: 5% !important; /* Increased to match GreatYOP */
    padding-right: 5% !important; /* Increased to match GreatYOP */
  }
}

@media (min-width: 1200px) {
  .px-4, .px-6, .px-8, .sm\\:px-6, .lg\\:px-8 {
    padding-left: 8% !important; /* Increased to match GreatYOP's wider margins */
    padding-right: 8% !important; /* Increased to match GreatYOP's wider margins */
  }
}

/* Container optimization for GreatYOP style */
.container {
  width: 100% !important;
  padding-left: 5% !important; /* Increased to match GreatYOP */
  padding-right: 5% !important; /* Increased to match GreatYOP */
  margin-left: auto !important;
  margin-right: auto !important;
}

@media (min-width: 1200px) {
  .container {
    padding-left: 8% !important; /* Increased to match GreatYOP's wider margins */
    padding-right: 8% !important; /* Increased to match GreatYOP's wider margins */
  }
}

/* Adjust grid gaps for better space utilization */
.gap-8 {
  gap: 1rem !important; /* Reduced from 2rem */
}

.gap-6 {
  gap: 0.75rem !important; /* Reduced from 1.5rem */
}

.gap-4 {
  gap: 0.5rem !important; /* Reduced from 1rem */
}

/* Professional spacing for sections */
.py-16 {
  padding-top: 3rem !important; /* Reduced from 4rem */
  padding-bottom: 3rem !important; /* Reduced from 4rem */
}

.py-12 {
  padding-top: 2rem !important; /* Reduced from 3rem */
  padding-bottom: 2rem !important; /* Reduced from 3rem */
}

.py-8 {
  padding-top: 1.5rem !important; /* Reduced from 2rem */
  padding-bottom: 1.5rem !important; /* Reduced from 2rem */
}

/* Responsive adjustments for mobile */
@media (max-width: 640px) {
  .max-w-7xl,
  .max-w-6xl,
  .max-w-5xl,
  .max-w-4xl {
    max-width: 95% !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}

/* Optimize card layouts for better space usage */
.gy-pcard-wrap {
  gap: 1rem !important; /* Reduced gap between cards */
}

@media (min-width: 1024px) {
  .gy-pcard-wrap {
    gap: 1.5rem !important; /* Slightly reduced gap for desktop */
  }
}

/* Professional margins for content sections */
.mb-8 {
  margin-bottom: 1.5rem !important; /* Reduced from 2rem */
}

.mb-6 {
  margin-bottom: 1rem !important; /* Reduced from 1.5rem */
}

.mt-8 {
  margin-top: 1.5rem !important; /* Reduced from 2rem */
}

.mt-6 {
  margin-top: 1rem !important; /* Reduced from 1.5rem */
}
