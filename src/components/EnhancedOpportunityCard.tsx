import React, { useState, useEffect } from 'react';
import { calculateDaysRemaining } from '../utils/dateFormatter';
import { useLanguage } from '../context/LanguageContext';
import { constructImageUrl, handleImageError, getImagePlaceholder, preloadImageWithRetry, ImageLoadState, reportImageError } from '../utils/imageUtils';

interface EnhancedOpportunityCardProps {
  id: number;
  title: string;
  thumbnail?: string;
  deadline: string;
  isActive: boolean;
  onClick: (id: number) => void;
  type: 'internship' | 'training' | 'conference' | 'workshop' | 'competition';
  organization: string;
  location: string;
  isRemote: boolean;
  featured?: boolean;
  index?: number;
  variant?: 'default' | 'greatyop';
}

const EnhancedOpportunityCard: React.FC<EnhancedOpportunityCardProps> = ({
  id,
  title,
  thumbnail,
  deadline,
  isActive,
  onClick,
  type,
  organization,
  location,
  isRemote,
  featured = false,
  index = 0,
  variant = 'default',
}) => {
  const [imageUrl, setImageUrl] = useState<string>(getImagePlaceholder());
  const [imageState, setImageState] = useState<ImageLoadState>(ImageLoadState.LOADING);

  const { translations, language } = useLanguage();
  const { formattedText, isOpen: isNotExpired } = calculateDaysRemaining(deadline, language);

  // Use the calculated isOpen status if available, otherwise use the prop
  const opportunityStatus = isNotExpired !== undefined ? isNotExpired : isActive;

  // Animation delay for staggered entrance
  const animationDelay = `${index * 100}ms`;

  // Get type icon and label
  const getTypeIcon = (type: string): string => {
    const icons = {
      internship: '🎓',
      training: '📚',
      conference: '🎤',
      workshop: '🔧',
      competition: '🏆'
    };
    return icons[type as keyof typeof icons] || '📋';
  };

  const getTypeLabel = (type: string): string => {
    const labels = {
      internship: 'Stage',
      training: 'Formation',
      conference: 'Conférence',
      workshop: 'Atelier',
      competition: 'Concours'
    };
    return labels[type as keyof typeof labels] || type;
  };

  // Handle image loading
  useEffect(() => {
    if (thumbnail) {
      const imageToLoad = constructImageUrl(thumbnail);
      
      preloadImageWithRetry(imageToLoad, 3)
        .then((url) => {
          setImageUrl(url);
          setImageState(ImageLoadState.LOADED);
        })
        .catch((error) => {
          console.warn('Failed to load opportunity image:', error);
          reportImageError(imageToLoad, error.message);
          setImageUrl(getImagePlaceholder());
          setImageState(ImageLoadState.ERROR);
        });
    } else {
      setImageUrl(getImagePlaceholder());
      setImageState(ImageLoadState.ERROR);
    }
  }, [thumbnail]);

  // Render GreatYOP style card - exact same design as scholarship cards
  if (variant === 'greatyop') {
    return (
      <div className="gy-post-card">
        <article className="post type-post status-publish format-standard has-post-thumbnail hentry">
          {/* Image section */}
          <div className="gyp-article-thumb">
            <button
              type="button"
              className="w-full h-full border-0 p-0 bg-transparent cursor-pointer"
              onClick={() => onClick(id)}
            >
              {imageState === ImageLoadState.LOADING && (
                <div className="w-full h-full bg-gray-200 animate-pulse flex items-center justify-center">
                  <div className="text-gray-400 text-sm">Loading...</div>
                </div>
              )}

              <img
                src={imageUrl}
                alt={title}
                className={`w-full h-full object-cover transition-all duration-300 ${
                  imageState === ImageLoadState.LOADED ? 'opacity-100' : 'opacity-0'
                }`}
                onLoad={() => setImageState(ImageLoadState.LOADED)}
                onError={(e) => handleImageError(e)}
                loading="lazy"
              />
            </button>
          </div>

          {/* Header section - exact same as scholarship cards */}
          <div className="gyp-archive-post-header-wrapper">
            <div className="entry-header">
              <h2 className="entry-title">
                <button
                  type="button"
                  className="text-left w-full border-0 p-0 bg-transparent cursor-pointer hover:text-blue-600 transition-colors duration-200"
                  style={{
                    color: '#353535',
                    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif',
                    fontSize: '20px',
                    lineHeight: '1.3',
                    fontWeight: '600',
                    margin: '0 0 6px',
                    textTransform: 'capitalize'
                  }}
                  onClick={() => onClick(id)}
                >
                  {title}
                </button>
              </h2>
            </div>
          </div>

          {/* Bottom section with left date and right organization+location - same layout as scholarships */}
          <div className="gy-pcard-bottom">
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
              {/* Left side: Calendar icon + Date */}
              <div style={{ display: 'flex', alignItems: 'center', flexShrink: 0 }}>
                <svg className="h-4 w-4 mr-1.5 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <span
                  className={`font-medium ${!opportunityStatus ? 'text-red-600' : ''}`}
                  style={{
                    fontSize: '13px',
                    color: !opportunityStatus ? '#dc2626' : '#6b7280',
                    fontWeight: '500'
                  }}
                >
                  {formattedText}
                </span>
              </div>

              {/* Right side: Organization and Location */}
              {(organization || location) && (
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px', flexShrink: 1, minWidth: 0 }}>
                  <svg className="h-4 w-4 text-gray-400 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                  <span
                    className="text-gray-600 truncate"
                    style={{
                      fontSize: '13px',
                      fontWeight: '500'
                    }}
                  >
                    {isRemote ? 'À distance' : location}
                  </span>
                </div>
              )}
            </div>
          </div>
        </article>
      </div>
    );
  }

  // Default enhanced card design
  return (
    <div
      className={`group relative bg-white rounded-2xl shadow-lg overflow-hidden w-full transition-all duration-500 hover:shadow-xl hover:shadow-primary/10 hover:-translate-y-2 cursor-pointer ${
        featured ? 'border-2 border-primary ring-2 ring-primary/20' : 'border border-gray-100 hover:border-primary/30'
      }`}
      onClick={() => onClick(id)}
      style={{ animationDelay }}
    >
      {/* Featured badge */}
      {featured && (
        <div className="absolute top-0 right-0 z-20">
          <div className="bg-primary text-white text-xs font-bold px-3 py-1 rounded-bl-lg">
            {translations.common.popular}
          </div>
        </div>
      )}

      {/* Image section */}
      <div className="relative overflow-hidden h-48">
        {imageState === ImageLoadState.LOADING && (
          <div className="w-full h-full bg-gray-200 animate-pulse flex items-center justify-center">
            <div className="text-gray-400 text-sm">Loading...</div>
          </div>
        )}
        
        <img
          src={imageUrl}
          alt={title}
          className={`w-full h-full object-cover transition-all duration-500 group-hover:scale-110 ${
            imageState === ImageLoadState.LOADED ? 'opacity-100' : 'opacity-0'
          }`}
          onLoad={() => setImageState(ImageLoadState.LOADED)}
          onError={(e) => handleImageError(e)}
          loading="lazy"
        />
        
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        
        {/* Type icon */}
        <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-full w-12 h-12 flex items-center justify-center shadow-lg">
          <span className="text-xl">{getTypeIcon(type)}</span>
        </div>
        
        {/* Status badge */}
        <div className="absolute top-4 right-4">
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
            opportunityStatus 
              ? 'bg-green-100 text-green-800' 
              : 'bg-red-100 text-red-800'
          }`}>
            {opportunityStatus ? 'Actif' : 'Expiré'}
          </span>
        </div>
      </div>

      {/* Content section */}
      <div className="p-6">
        <div className="flex items-center justify-between mb-3">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
            {getTypeLabel(type)}
          </span>
          <span className="text-sm text-gray-500 font-medium">
            {organization}
          </span>
        </div>
        
        <h3 className="text-lg font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-primary transition-colors duration-200">
          {title}
        </h3>
        
        {/* Location and deadline info */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm text-gray-600">
            <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
            </svg>
            <span>{isRemote ? 'À distance' : location}</span>
            {isRemote && location && (
              <span className="ml-1 text-gray-400">• {location}</span>
            )}
          </div>
          
          <div className="flex items-center text-sm text-gray-600">
            <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
            </svg>
            <span>{formattedText}</span>
          </div>
        </div>
        
        {/* Action button */}
        <div className="flex items-center justify-between">
          <button className="text-primary text-sm font-medium hover:text-primary-light transition-colors duration-200">
            Voir les détails
          </button>
          
          {opportunityStatus && (
            <button className="px-4 py-2 bg-gradient-to-r from-primary to-primary-light text-white text-sm font-medium rounded-lg hover:from-primary-light hover:to-primary shadow-md hover:shadow-lg transition-all duration-200">
              Postuler
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedOpportunityCard;
