import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import EnhancedOpportunityCard from './EnhancedOpportunityCard';
import GreatYOPPagination from './GreatYOPPagination';
import UnifiedSidebar from './UnifiedSidebar';
import CommentsSection from './CommentsSection';
import AdPlacement from './AdPlacement';
import { Spin, Alert } from 'antd';

interface Opportunity {
  id: number;
  title: string;
  description: string;
  type: 'internship' | 'training' | 'conference' | 'workshop' | 'competition';
  organization: string;
  location: string;
  isRemote: boolean;
  deadline: string;
  startDate?: string;
  endDate?: string;
  applicationLink?: string;
  thumbnail?: string;
  isActive: boolean;
  tags?: string[];
}

interface StandardOpportunityPageConfig {
  type: string;
  title: string;
  description: string;
  keywords: string;
  heroTitle: string;
  heroDescription: string;
  infoTitle: string;
  infoContent: string;
  benefits: string[];
  apiEndpoint: string;
}

interface StandardOpportunityPageProps {
  config: StandardOpportunityPageConfig;
}

const StandardOpportunityPage: React.FC<StandardOpportunityPageProps> = ({ config }) => {
  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 6,
    total: 0
  });

  const handleOpportunityClick = (id: number) => {
    window.location.href = `/opportunities/${id}`;
  };

  const fetchOpportunities = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/opportunities/type/${encodeURIComponent(config.type)}?page=${pagination.page}&limit=${pagination.limit}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setOpportunities(data.data || []);
        setPagination(prev => ({
          ...prev,
          total: data.pagination?.total || data.data?.length || 0
        }));
      } else {
        throw new Error(data.message || 'Failed to fetch opportunities');
      }
    } catch (error) {
      console.error('Error fetching opportunities:', error);
      setError('Impossible de charger les opportunités. Veuillez réessayer plus tard.');
      setOpportunities([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOpportunities();
  }, [pagination.page, config.type]); // eslint-disable-line react-hooks/exhaustive-deps

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const sidebarConfig = {
    type: 'opportunities' as const,
    currentItem: config.type,
    limit: 10
  };

  return (
    <>
      <Helmet>
        <title>{config.title}</title>
        <meta name="description" content={config.description} />
        <meta name="keywords" content={config.keywords} />
      </Helmet>

      {/* Hero Section - GreatYOP Style */}
      <section className="relative bg-gradient-to-br from-gray-900 via-primary-dark to-primary overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <img
            src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1200&q=80"
            alt="Professional opportunities"
            className="w-full h-full object-cover opacity-20"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-primary/80 to-primary-dark/80"></div>
        </div>

        {/* Hero Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-20">
          <div className="text-center">
            <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-4 leading-tight">
              {config.heroTitle}
            </h1>
            <div className="max-w-4xl mx-auto">
              <p className="text-base md:text-lg text-white/90 leading-relaxed">
                {config.heroDescription}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <div className="bg-gray-50 min-h-screen">
        {/* Information Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="bg-white rounded-xl shadow-sm p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">{config.infoTitle}</h2>
            <p className="text-gray-700 text-lg leading-relaxed mb-8">{config.infoContent}</p>

            <div className="grid md:grid-cols-2 gap-6">
              {config.benefits.map((benefit, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mt-0.5">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="text-gray-700 leading-relaxed">{benefit}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Content Section with Sidebar */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 pb-16">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Main Content - 2x3 Grid */}
            <div className="flex-1">
              {/* Opportunities Grid - 2x3 Layout */}
              <div id="opportunities-section" className="mb-8">
                {loading ? (
                  <div className="flex justify-center items-center py-16">
                    <Spin size="large" tip="Chargement des opportunités..." />
                  </div>
                ) : error ? (
                  <Alert
                    message="Erreur"
                    description={error}
                    type="error"
                    showIcon
                    className="mb-6 rounded-xl shadow-md"
                  />
                ) : (
                  <>
                    {/* Mobile Ad - Only visible on small screens */}
                    <div className="mb-8 md:hidden">
                      <AdPlacement
                        adSlot="4567890123"
                        adSize="rectangle"
                        responsive={true}
                        fullWidth={true}
                      />
                    </div>

                    {/* 2x3 Grid Layout */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6 mb-8">
                      {opportunities.map((opportunity, index) => (
                        <EnhancedOpportunityCard
                          key={opportunity.id}
                          id={opportunity.id}
                          title={opportunity.title}
                          thumbnail={opportunity.thumbnail}
                          deadline={opportunity.deadline}
                          isActive={opportunity.isActive}
                          type={opportunity.type}
                          organization={opportunity.organization}
                          location={opportunity.location}
                          isRemote={opportunity.isRemote}
                          onClick={handleOpportunityClick}
                          index={index}
                          variant="greatyop"
                        />
                      ))}
                    </div>

                    {/* GreatYOP Pagination */}
                    {(pagination.total > pagination.limit || opportunities.length > 0) && (
                      <GreatYOPPagination
                        current={pagination.page}
                        total={Math.max(pagination.total, opportunities.length)}
                        pageSize={pagination.limit}
                        onChange={handlePageChange}
                        showQuickJumper={false}
                      />
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Enhanced Sidebar - Wider for 2x4 layout */}
            <div className="lg:w-96">
              <UnifiedSidebar config={sidebarConfig} />

              {/* Desktop Ad - Only visible on large screens */}
              <div className="hidden lg:block mt-8">
                <AdPlacement
                  adSlot="9876543210"
                  adSize="rectangle"
                  responsive={false}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Comments Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
          <CommentsSection pageType="opportunity" pageId={config.type} />
        </div>
      </div>
    </>
  );
};

export default StandardOpportunityPage;
