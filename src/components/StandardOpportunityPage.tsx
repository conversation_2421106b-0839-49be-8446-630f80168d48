import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import EnhancedOpportunityCard from './EnhancedOpportunityCard';
import GreatYOPPagination from './GreatYOPPagination';
import UnifiedSidebar from './UnifiedSidebar';
import CommentsSection from './CommentsSection';
import AdPlacement from './AdPlacement';
import { Spin, Alert } from 'antd';

interface Opportunity {
  id: number;
  title: string;
  description: string;
  type: 'internship' | 'training' | 'conference' | 'workshop' | 'competition';
  organization: string;
  location: string;
  isRemote: boolean;
  deadline: string;
  startDate?: string;
  endDate?: string;
  applicationLink?: string;
  thumbnail?: string;
  isActive: boolean;
  tags?: string[];
}

interface StandardOpportunityPageConfig {
  type: string;
  title: string;
  description: string;
  keywords: string;
  heroTitle: string;
  heroDescription: string;
  infoTitle: string;
  infoContent: string;
  benefits: string[];
  apiEndpoint: string;
}

interface StandardOpportunityPageProps {
  config: StandardOpportunityPageConfig;
}

const StandardOpportunityPage: React.FC<StandardOpportunityPageProps> = ({ config }) => {
  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 6,
    total: 0
  });

  const handleOpportunityClick = (id: number) => {
    window.location.href = `/opportunities/${id}`;
  };

  const fetchOpportunities = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Build query parameters
      const params = new URLSearchParams();
      params.append('page', pagination.page.toString());
      params.append('limit', pagination.limit.toString());
      params.append('active', 'true');
      params.append('type', config.type);
      params.append('orderBy', 'deadline');
      params.append('orderDirection', 'ASC');

      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/opportunities?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('StandardOpportunityPage API response:', data);

      if (data.success) {
        // Handle the correct API response format: { success: true, data: { opportunities: [...], pagination: {...} } }
        const opportunitiesData = data.data?.opportunities || data.opportunities || [];
        const paginationData = data.data?.pagination || data.pagination || {};

        setOpportunities(opportunitiesData);
        setPagination(prev => ({
          ...prev,
          total: paginationData.total || opportunitiesData.length || 0,
          totalPages: Math.ceil((paginationData.total || opportunitiesData.length || 0) / pagination.limit),
          hasNextPage: paginationData.hasNextPage || false,
          hasPreviousPage: paginationData.hasPreviousPage || false
        }));
      } else {
        throw new Error(data.message || 'Failed to fetch opportunities');
      }
    } catch (error) {
      console.error('Error fetching opportunities:', error);
      setError('Impossible de charger les opportunités. Veuillez réessayer plus tard.');
      setOpportunities([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOpportunities();
  }, [pagination.page, config.type]); // eslint-disable-line react-hooks/exhaustive-deps

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const sidebarConfig = {
    type: 'opportunities' as const,
    currentItem: config.type,
    limit: 10
  };

  return (
    <>
      <Helmet>
        <title>{config.title}</title>
        <meta name="description" content={config.description} />
        <meta name="keywords" content={config.keywords} />
      </Helmet>

      <div className="min-h-screen bg-gradient-to-br from-primary-50/30 via-white to-primary-100/20 opportunity-page">
        {/* Compact Hero Section - Matching Website Design */}
        <section className="bg-white pt-20 pb-4" style={{ paddingTop: '4rem' }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center mb-3">
              <span className="text-3xl mr-3">💼</span>
              <h1 style={{
                fontSize: '24px',
                marginBottom: '0',
                color: '#2563eb',
                fontWeight: '700',
                textTransform: 'capitalize'
              }}>
                {config.heroTitle}
              </h1>
            </div>

            <div className="archive-description">
              <p style={{
                marginBottom: '15px',
                textAlign: 'justify',
                color: '#3d3d3d',
                fontSize: '17px',
                lineHeight: '30px',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif'
              }}>
                {config.heroDescription}
              </p>
              <p style={{
                marginBottom: '20px',
                textAlign: 'justify',
                color: '#3d3d3d',
                fontSize: '17px',
                lineHeight: '30px',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif'
              }}>
                {config.infoContent}
              </p>
            </div>

            {/* Quick Stats */}
            <div className="flex flex-wrap gap-3 mt-3">
              <div className="flex items-center bg-blue-50 px-3 py-1.5 rounded-lg">
                <svg className="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 8v6a2 2 0 002 2h4a2 2 0 002-2V8M8 8V6a2 2 0 012-2h4a2 2 0 012 2v2" />
                </svg>
                <span className="text-sm font-medium text-blue-800">Opportunités Vérifiées</span>
              </div>
              <div className="flex items-center bg-green-50 px-3 py-1.5 rounded-lg">
                <svg className="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
                <span className="text-sm font-medium text-green-800">Développement Professionnel</span>
              </div>
              <div className="flex items-center bg-purple-50 px-3 py-1.5 rounded-lg">
                <svg className="w-4 h-4 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-sm font-medium text-purple-800">Toutes Spécialités</span>
              </div>
            </div>
          </div>
        </section>

        {/* Desktop Ad - Only visible on large screens */}
        <div className="hidden lg:block py-8 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <AdPlacement
              adSlot="1234567890"
              adSize="leaderboard"
              responsive={true}
            />
          </div>
        </div>

        {/* Content Section with Sidebar Layout */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Main Content - 2x3 Grid */}
            <div className="flex-1">
              {/* Opportunities Grid */}
              <div id="opportunities-section" className="mb-8">

                {loading ? (
                  <div className="flex justify-center items-center py-16">
                    <Spin size="large" tip="Chargement des opportunités..." />
                  </div>
                ) : error ? (
                  <Alert
                    message="Erreur"
                    description={error}
                    type="error"
                    showIcon
                    className="mb-6 rounded-xl shadow-md"
                  />
                ) : (
                  <>
                    {/* Mobile Ad - Only visible on small screens */}
                    <div className="mb-8 md:hidden">
                      <AdPlacement
                        adSlot="4567890123"
                        adSize="rectangle"
                        responsive={true}
                        fullWidth={true}
                      />
                    </div>

                    {/* 2x3 Grid Layout - Country page style */}
                    <div className="country-page">
                      <div className="gy-pcard-wrap">
                        {opportunities.map((opportunity, index) => (
                          <EnhancedOpportunityCard
                            key={opportunity.id}
                            id={opportunity.id}
                            title={opportunity.title}
                            thumbnail={opportunity.thumbnail}
                            deadline={opportunity.deadline}
                            isActive={opportunity.isActive}
                            type={opportunity.type}
                            organization={opportunity.organization}
                            location={opportunity.location}
                            isRemote={opportunity.isRemote}
                            onClick={handleOpportunityClick}
                            index={index}
                            variant="greatyop"
                          />
                        ))}
                      </div>
                    </div>

                    {/* GreatYOP Pagination */}
                    {(pagination.total > pagination.limit || opportunities.length > 0) && (
                      <GreatYOPPagination
                        current={pagination.page}
                        total={Math.max(pagination.total, opportunities.length)}
                        pageSize={pagination.limit}
                        onChange={handlePageChange}
                        showQuickJumper={false}
                      />
                    )}

                    {/* No Results Message */}
                    {opportunities.length === 0 && (
                      <div className="text-center py-16 bg-gray-50 rounded-2xl shadow-sm border border-gray-100">
                        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 8v6a2 2 0 002 2h4a2 2 0 002-2V8M8 8V6a2 2 0 012-2h4a2 2 0 012 2v2" />
                        </svg>
                        <h3 className="mt-4 text-lg font-medium text-gray-900">Aucune opportunité trouvée</h3>
                        <p className="mt-2 text-sm text-gray-500 max-w-md mx-auto">
                          Essayez d'ajuster vos critères de recherche pour trouver ce que vous cherchez.
                        </p>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Right Sidebar */}
            <div className="lg:w-96">
              {/* Newsletter Section */}
              <div className="mb-8">
                <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
                  <div className="p-6">
                    <div className="flex items-center mb-4">
                      <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 mr-3">
                        <svg className="w-5 h-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-gray-900">Newsletter Opportunités</h3>
                        <p className="text-sm text-gray-600">Développement professionnel</p>
                      </div>
                    </div>

                    <p className="text-sm text-gray-600 mb-4 leading-relaxed">
                      Recevez les dernières opportunités professionnelles directement dans votre boîte mail.
                    </p>

                    <form className="space-y-4">
                      <div className="flex gap-3">
                        <input
                          type="email"
                          placeholder="<EMAIL>"
                          className="flex-1 px-4 py-3 text-sm rounded-lg border border-gray-300 focus:ring-primary focus:border-primary focus:outline-none focus:ring-2 transition-colors duration-200"
                        />
                        <button
                          type="submit"
                          className="px-6 py-3 bg-primary text-white text-sm font-medium rounded-lg hover:bg-primary-dark transition-colors duration-300 whitespace-nowrap"
                        >
                          S'abonner
                        </button>
                      </div>

                      <div className="flex items-start">
                        <input
                          id="privacy-sidebar"
                          type="checkbox"
                          className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-0.5"
                        />
                        <label htmlFor="privacy-sidebar" className="ml-3 block text-sm text-gray-600 leading-relaxed">
                          J'accepte de recevoir des emails concernant les opportunités professionnelles
                        </label>
                      </div>
                    </form>
                  </div>
                </div>
              </div>

              {/* Latest Opportunities Section */}
              <div className="mb-8">
                <UnifiedSidebar config={sidebarConfig} />
              </div>

              {/* Desktop Ad - Only visible on large screens */}
              <div className="hidden lg:block">
                <AdPlacement
                  adSlot="9876543210"
                  adSize="rectangle"
                  responsive={false}
                />
              </div>
            </div>
          </div>

          {/* Discussion Section at the End */}
          <div className="mt-16">
            <CommentsSection pageType="opportunity" pageId={config.type} />
          </div>
        </div>
      </div>
    </>
  );
};

export default StandardOpportunityPage;
