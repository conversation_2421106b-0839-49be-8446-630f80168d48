import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Scholarship } from './ScholarshipGrid';
import EnhancedScholarshipCard from './EnhancedScholarshipCard';
import { Government, Building, Organization } from './icons/index';

interface EnhancedFundingSourcesSectionProps {
  governmentScholarships: Scholarship[];
  universityScholarships: Scholarship[];
  organizationScholarships: Scholarship[];
  loading: boolean;
  onScholarshipClick: (id: number) => void;
}

const EnhancedFundingSourcesSection: React.FC<EnhancedFundingSourcesSectionProps> = ({
  governmentScholarships,
  universityScholarships,
  organizationScholarships,
  loading,
  onScholarshipClick
}) => {
  const [activeTab, setActiveTab] = useState('government');



  // Get active scholarships based on tab
  const getActiveScholarships = () => {
    switch (activeTab) {
      case 'government':
        return governmentScholarships;
      case 'university':
        return universityScholarships;
      case 'organization':
        return organizationScholarships;
      default:
        return governmentScholarships;
    }
  };

  const activeScholarships = getActiveScholarships();

  // Tab configuration
  const tabs = [
    {
      id: 'government',
      label: 'Gouvernements',
      icon: <Government className="w-6 h-6" />,
      color: 'blue',
      bgClass: 'from-blue-500 to-blue-700'
    },
    {
      id: 'university',
      label: 'Universités',
      icon: <Building className="w-6 h-6" />,
      color: 'purple',
      bgClass: 'from-purple-500 to-purple-700'
    },
    {
      id: 'organization',
      label: 'Organisations',
      icon: <Organization className="w-6 h-6" />,
      color: 'green',
      bgClass: 'from-green-500 to-green-700'
    }
  ];

  // Get active tab data
  const activeTabData = tabs.find(tab => tab.id === activeTab) || tabs[0];

  return (
    <section className="py-3 pt-2 bg-gradient-to-br from-primary-50/20 via-gray-50 to-primary-100/40 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">


        {/* Interactive tabs - Professional framed buttons */}
        <div className="flex flex-wrap justify-center gap-3 mb-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => {
                setActiveTab(tab.id);
                // Just filter on the same page, don't navigate
              }}
              style={{
                border: '1px solid #6b7280',
                backgroundColor: activeTab === tab.id ? '#eff6ff' : 'transparent',
                color: activeTab === tab.id ? '#2563eb' : '#374151'
              }}
              className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-blue-50 hover:text-blue-600`}
            >
              <span className="mr-2">
                {tab.icon}
              </span>
              {tab.label}
            </button>
          ))}
        </div>



        {/* Scholarships grid - 3x2 grid */}
        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-md overflow-hidden animate-pulse">
                <div className="aspect-[16/9] bg-gray-200"></div>
                <div className="p-5">
                  <div className="h-5 bg-gray-200 rounded w-3/4 mb-3"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
                  <div className="h-4 bg-gray-200 rounded w-full mb-3"></div>
                  <div className="h-10 bg-gray-100 rounded w-full mt-4"></div>
                </div>
              </div>
            ))}
          </div>
        ) : activeScholarships.length > 0 ? (
          <div className="gy-pcard-wrap">
            {activeScholarships.slice(0, 6).map((scholarship, index) => (
              <EnhancedScholarshipCard
                key={scholarship.id}
                id={scholarship.id}
                title={scholarship.title}
                thumbnail={scholarship.thumbnail}
                deadline={scholarship.deadline}
                isOpen={scholarship.isOpen}
                level={scholarship.level}
                fundingSource={
                  activeTab === 'government' ? 'Gouvernement' :
                  activeTab === 'university' ? 'Université' : 'Organisation'
                }
                country={scholarship.country}
                onClick={onScholarshipClick}
                index={index}
                variant="greatyop"
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12 bg-white rounded-2xl shadow-sm">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="mt-2 text-lg font-medium text-gray-900">Aucune bourse trouvée</h3>
            <p className="mt-1 text-gray-500">Aucune bourse n'est disponible pour cette source de financement.</p>
          </div>
        )}

        {/* Call to action */}
        <div className="mt-6 flex justify-center">
          <Link
            to={`/scholarships?source=${
              activeTab === 'government' ? 'Gouvernement' :
              activeTab === 'university' ? 'Université' : 'Organisation'
            }`}
            className={`inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-xl text-white shadow-md transition-colors duration-300 ${
              activeTab === 'government' ? 'bg-blue-600 hover:bg-blue-700' :
              activeTab === 'university' ? 'bg-purple-600 hover:bg-purple-700' :
              'bg-green-600 hover:bg-green-700'
            }`}
          >
            Voir toutes les bourses {activeTabData.label.toLowerCase()}
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default EnhancedFundingSourcesSection;
