import React, { useEffect } from 'react';
import ProfessionalSidebar from './ProfessionalSidebar';
import { SidebarConfig } from '../services/sidebarService';
import dataPrefetcher from '../utils/dataPrefetcher';

interface HeroSection {
  title: string;
  subtitle?: string;
  icon?: string;
  backgroundColor?: string;
  textColor?: string;
}

interface StatisticsData {
  total: number;
  active: number;
  inactive?: number;
  label: string;
  activeLabel: string;
  inactiveLabel?: string;
}

interface FilterOption {
  key: string;
  label: string;
  value: string;
  options: Array<{ value: string; label: string }>;
}

interface ProfessionalPageLayoutProps {
  hero: HeroSection;
  statistics?: StatisticsData;
  filters?: FilterOption[];
  onFilterChange?: (filters: Record<string, string>) => void;
  sidebarConfig: SidebarConfig;
  children: React.ReactNode;
  loading?: boolean;
  className?: string;
}

const ProfessionalPageLayout: React.FC<ProfessionalPageLayoutProps> = ({
  hero,
  statistics,
  filters = [],
  onFilterChange,
  sidebarConfig,
  children,
  loading = false,
  className = ''
}) => {
  const [currentFilters, setCurrentFilters] = React.useState<Record<string, string>>({});

  // Prefetch related data when component mounts
  useEffect(() => {
    // Prefetch common routes on initial load
    dataPrefetcher.prefetchCommonRoutes();

    // Prefetch related data based on current page
    if (sidebarConfig.currentItem) {
      dataPrefetcher.prefetchRelatedData(sidebarConfig.type, sidebarConfig.currentItem);
    }
  }, [sidebarConfig]);

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...currentFilters, [key]: value };
    setCurrentFilters(newFilters);
    onFilterChange?.(newFilters);
  };

  const getHeroBackgroundClass = () => {
    if (hero.backgroundColor) {
      return hero.backgroundColor;
    }
    
    // Default colors based on page type
    switch (sidebarConfig.type) {
      case 'countries':
        return 'bg-gradient-to-r from-blue-600 to-blue-800';
      case 'levels':
        return 'bg-gradient-to-r from-green-600 to-green-800';
      case 'opportunities':
        return 'bg-gradient-to-r from-purple-600 to-purple-800';
      default:
        return 'bg-gradient-to-r from-gray-600 to-gray-800';
    }
  };

  const renderHeroSection = () => (
    <div className="bg-white pt-20 pb-4">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="w-full">
          <h1
            style={{
              color: '#3d3d3d',
              fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif',
              fontSize: '19px',
              lineHeight: '1.3',
              fontWeight: '600',
              marginBottom: '10px',
              textTransform: 'capitalize'
            }}
          >
            {hero.title}
          </h1>

          {hero.subtitle && (
            <p
              style={{
                color: '#3d3d3d',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif',
                fontSize: '17px',
                lineHeight: '30px',
                marginBottom: '15px',
                textAlign: 'justify'
              }}
            >
              {hero.subtitle}
            </p>
          )}

          <p
            style={{
              color: '#3d3d3d',
              fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif',
              fontSize: '17px',
              lineHeight: '30px',
              marginBottom: '15px',
              textAlign: 'justify'
            }}
          >
            Bourses d'études et opportunités pour les jeunes d'entreprendre des études supérieures sont répertoriées sur cette page. Offres de bourses entièrement financées et partiellement financées des gouvernements, organisations à but lucratif et non lucratif, universités accréditées, entreprises et fondations pour soutenir les étudiants internationaux et les étudiants des pays en développement.
          </p>

          <p
            style={{
              color: '#3d3d3d',
              fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif',
              fontSize: '17px',
              lineHeight: '30px',
              marginBottom: '30px',
              textAlign: 'justify'
            }}
          >
            Pour postuler à un programme d'études, veuillez cliquer sur la bourse qui vous intéresse. Vous serez redirigé vers une page qui contient des informations détaillées sur les critères d'éligibilité, les avantages, la procédure de candidature, les dates limites, etc. Assurez-vous de répondre aux critères d'éligibilité et suivez les instructions pour soumettre votre candidature avant la date limite.
          </p>
        </div>
      </div>
    </div>
  );

  const renderStatistics = () => {
    if (!statistics) return null;

    const statsData = [
      {
        value: statistics.total,
        label: statistics.label,
        color: 'blue',
        icon: '📊',
        gradient: 'from-blue-500 to-blue-600'
      },
      {
        value: statistics.active,
        label: statistics.activeLabel,
        color: 'green',
        icon: '✅',
        gradient: 'from-green-500 to-green-600'
      },
      ...(statistics.inactive !== undefined && statistics.inactiveLabel ? [{
        value: statistics.inactive,
        label: statistics.inactiveLabel,
        color: 'red',
        icon: '❌',
        gradient: 'from-red-500 to-red-600'
      }] : [])
    ];

    return (
      <div className="bg-gray-50 border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {statsData.map((stat, index) => (
              <div key={index} className="relative">
                <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 text-center transform hover:scale-105 transition-transform duration-300">
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <div className={`w-12 h-12 bg-gradient-to-r ${stat.gradient} rounded-full flex items-center justify-center shadow-lg`}>
                      <span className="text-white text-xl">{stat.icon}</span>
                    </div>
                  </div>
                  <div className="mt-6">
                    <div className={`text-4xl font-bold bg-gradient-to-r ${stat.gradient} bg-clip-text text-transparent mb-2`}>
                      {stat.value.toLocaleString()}
                    </div>
                    <div className="text-gray-600 font-medium">{stat.label}</div>
                  </div>

                  {/* Progress bar for visual appeal */}
                  <div className="mt-4">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`bg-gradient-to-r ${stat.gradient} h-2 rounded-full transition-all duration-1000 ease-out`}
                        style={{
                          width: `${Math.min((stat.value / Math.max(...statsData.map(s => s.value))) * 100, 100)}%`
                        }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderFilters = () => {
    if (filters.length === 0) return null;

    return (
      <div className="bg-gray-50 border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-wrap gap-4">
            {filters.map((filter) => (
              <div key={filter.key} className="min-w-0 flex-1 md:flex-none md:w-48">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {filter.label}
                </label>
                <select
                  value={currentFilters[filter.key] || ''}
                  onChange={(e) => handleFilterChange(filter.key, e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Tous</option>
                  {filter.options.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderMainContent = () => (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="flex flex-col lg:flex-row gap-8">
        {/* Main Content */}
        <div className="lg:w-2/3">
          {children}
        </div>

        {/* Sidebar */}
        <ProfessionalSidebar config={sidebarConfig} />
      </div>
    </div>
  );

  return (
    <>

      <div className={`min-h-screen bg-gray-50 ${className}`}>
        {/* Hero Section */}
        <div>
          {renderHeroSection()}
        </div>

        {/* Statistics Section */}
        <div>
          {renderStatistics()}
        </div>

        {/* Filters Section */}
        <div>
          {renderFilters()}
        </div>

        {/* Main Content with Sidebar */}
        <div>
          {renderMainContent()}
        </div>
      </div>
    </>
  );
};

/**
 * Professional Content Grid Component
 * Unified grid for displaying scholarships or opportunities
 */
interface ContentGridProps {
  items: any[];
  loading: boolean;
  emptyMessage: string;
  emptyIcon: string;
  renderItem: (item: any) => React.ReactNode;
  pagination?: {
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
  };
}

export const ProfessionalContentGrid: React.FC<ContentGridProps> = ({
  items,
  loading,
  emptyMessage,
  emptyIcon,
  renderItem,
  pagination
}) => {
  const renderLoadingGrid = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {[...Array(6)].map((_, index) => (
        <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
          <div className="aspect-[16/9] bg-gray-200"></div>
          <div className="p-6">
            <div className="h-5 bg-gray-200 rounded w-3/4 mb-3"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
            <div className="h-4 bg-gray-200 rounded w-full mb-3"></div>
            <div className="h-10 bg-gray-100 rounded w-full mt-4"></div>
          </div>
        </div>
      ))}
    </div>
  );

  const renderEmptyState = () => (
    <div className="text-center py-12">
      <div className="text-6xl mb-4">{emptyIcon}</div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">
        Aucun résultat trouvé
      </h3>
      <p className="text-gray-600">
        {emptyMessage}
      </p>
    </div>
  );

  const renderPagination = () => {
    if (!pagination || pagination.totalPages <= 1) return null;

    const { currentPage, totalPages, onPageChange } = pagination;
    const pages = [];

    // Add page numbers
    for (let i = 1; i <= totalPages; i++) {
      if (
        i === 1 ||
        i === totalPages ||
        (i >= currentPage - 2 && i <= currentPage + 2)
      ) {
        pages.push(i);
      } else if (
        i === currentPage - 3 ||
        i === currentPage + 3
      ) {
        pages.push('...');
      }
    }

    return (
      <div className="flex justify-center items-center space-x-2 mt-8">
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="px-3 py-2 rounded-md border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Précédent
        </button>

        {pages.map((page, index) => (
          <button
            key={index}
            onClick={() => typeof page === 'number' ? onPageChange(page) : undefined}
            disabled={typeof page !== 'number'}
            className={`px-3 py-2 rounded-md border ${
              page === currentPage
                ? 'border-blue-500 bg-blue-500 text-white'
                : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
            } ${typeof page !== 'number' ? 'cursor-default' : 'cursor-pointer'}`}
          >
            {page}
          </button>
        ))}

        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="px-3 py-2 rounded-md border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Suivant
        </button>
      </div>
    );
  };

  if (loading) {
    return renderLoadingGrid();
  }

  if (items.length === 0) {
    return renderEmptyState();
  }

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {items.map(renderItem)}
      </div>
      {renderPagination()}
    </>
  );
};

export default ProfessionalPageLayout;
