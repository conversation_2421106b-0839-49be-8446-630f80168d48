import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

interface Country {
  country: string;
  count: number;
}

interface CountriesSidebarProps {
  currentCountry: string;
  className?: string;
}

// Helper function to get country flag
const getCountryFlag = (countryName: string): string => {
  const flagMap: { [key: string]: string } = {
    'France': '🇫🇷',
    'Canada': '🇨🇦',
    'États-Unis': '🇺🇸',
    'Allemagne': '🇩🇪',
    'Royaume-Uni': '🇬🇧',
    'Australie': '🇦🇺',
    'Suisse': '🇨🇭',
    'Belgique': '🇧🇪',
    'Pays-Bas': '🇳🇱',
    'Suède': '🇸🇪',
    'Norvège': '🇳🇴',
    'Danemark': '🇩🇰',
    'Finlande': '🇫🇮',
    'Japon': '🇯🇵',
    'Corée du Sud': '🇰🇷',
    'Singapour': '🇸🇬',
    'Nouvelle-Zélande': '🇳🇿',
    'Italie': '🇮🇹',
    'Espagne': '🇪🇸',
    'Portugal': '🇵🇹',
    'Brazil': '🇧🇷',
    'Brésil': '🇧🇷'
  };
  return flagMap[countryName] || '🌍';
};

const CountriesSidebar: React.FC<CountriesSidebarProps> = ({ currentCountry, className = '' }) => {
  const [countries, setCountries] = useState<Country[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAll, setShowAll] = useState(false);

  useEffect(() => {
    const fetchCountries = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
        const response = await fetch(`${apiUrl}/api/countries`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch countries');
        }

        const result = await response.json();
        const countriesData = result.data || result;
        
        // Filter out current country and sort by scholarship count
        const filteredCountries = countriesData
          .filter((country: Country) => country.country !== currentCountry)
          .sort((a: Country, b: Country) => b.count - a.count);
        
        setCountries(filteredCountries);
      } catch (error) {
        console.error('Error fetching countries:', error);
        setError('Failed to load countries');
        setCountries([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCountries();
  }, [currentCountry]);

  if (loading) {
    return (
      <div className={`bg-white rounded-xl shadow-lg p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="h-4 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-xl shadow-lg p-6 ${className}`}>
        <div className="text-center text-gray-500">
          <svg className="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="text-sm">{error}</p>
        </div>
      </div>
    );
  }

  const displayedCountries = showAll ? countries : countries.slice(0, 8);

  return (
    <div className={`bg-white rounded-xl shadow-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-primary to-primary-dark p-3">
        <h3 className="text-base font-bold text-white flex items-center">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Autres Pays
        </h3>
      </div>

      {/* Countries List */}
      <div className="p-3">
        {countries.length > 0 ? (
          <div className="space-y-1">
            {displayedCountries.map((country) => (
              <Link
                key={country.country}
                to={`/pays/${encodeURIComponent(country.country)}`}
                className="group flex items-center p-2 rounded-lg hover:bg-primary/5 transition-all duration-200"
              >
                <div className="min-w-0 flex-1">
                  <span className="text-xs font-medium text-gray-900 group-hover:text-primary transition-colors duration-200 block truncate">
                    Scholarships in
                  </span>
                  <span className="text-sm font-semibold text-gray-800 group-hover:text-primary-dark transition-colors duration-200 block truncate">
                    {country.country}
                  </span>
                </div>
              </Link>
            ))}

            {countries.length > 8 && (
              <button
                onClick={() => setShowAll(!showAll)}
                className="w-full p-2 text-center text-sm font-medium text-primary hover:text-primary-dark hover:bg-primary/5 rounded-lg transition-all duration-200"
              >
                {showAll ? 'Voir moins' : `Voir plus (${countries.length - 8} autres)`}
              </button>
            )}
          </div>
        ) : (
          <div className="text-center py-6">
            <div className="text-gray-400 mb-2">
              <svg className="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-xs text-gray-500">Aucun autre pays disponible</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CountriesSidebar;
