import React, { useState, useEffect } from 'react';
import { calculateDaysRemaining } from '../utils/dateFormatter';
import { constructImageUrl, handleImageError, getImagePlaceholder, preloadImageWithRetry, ImageLoadState, reportImageError } from '../utils/imageUtils';

interface ScholarshipCardProps {
  id: number;
  title: string;
  thumbnail: string;
  deadline: string;
  isOpen: boolean;
  onClick: (id: number) => void;
  level?: string;
  fundingSource?: string;
  country?: string;
}

const ScholarshipCard: React.FC<ScholarshipCardProps> = ({
  id,
  title,
  thumbnail,
  deadline,
  isOpen,
  onClick,
  level,
  country,
}) => {
  const [imageUrl, setImageUrl] = useState<string>(getImagePlaceholder());
  const [imageState, setImageState] = useState<ImageLoadState>(ImageLoadState.LOADING);

  const { formattedText, isOpen: isNotExpired } = calculateDaysRemaining(deadline);

  // Use the calculated isOpen status if available, otherwise use the prop
  const scholarshipStatus = isNotExpired !== undefined ? isNotExpired : isOpen;

  // Load optimized image
  useEffect(() => {
    const loadImage = async () => {
      setImageState(ImageLoadState.LOADING);

      try {
        // Try card-sized thumbnail first with enhanced error handling
        const cardUrl = constructImageUrl(thumbnail, 'card');
        const cardState = await preloadImageWithRetry(cardUrl, 0, (progress) => {
          if (progress.error) {
            reportImageError(cardUrl, progress.error);
          }
        });

        if (cardState === ImageLoadState.LOADED) {
          setImageUrl(cardUrl);
          setImageState(ImageLoadState.LOADED);
          return;
        }

        // Fallback to original image
        const originalUrl = constructImageUrl(thumbnail, 'original');
        const originalState = await preloadImageWithRetry(originalUrl, 0, (progress) => {
          if (progress.error) {
            reportImageError(originalUrl, progress.error);
          }
        });

        if (originalState === ImageLoadState.LOADED) {
          setImageUrl(originalUrl);
          setImageState(ImageLoadState.LOADED);
          return;
        }

        // Final fallback
        setImageUrl(constructImageUrl(null));
        setImageState(ImageLoadState.FALLBACK);
      } catch (error) {
        const errorMessage = `Error loading scholarship image: ${(error as Error).message}`;
        console.error(errorMessage);
        reportImageError(thumbnail || 'unknown', errorMessage);
        setImageUrl(constructImageUrl(null));
        setImageState(ImageLoadState.ERROR);
      }
    };

    loadImage();
  }, [thumbnail]);

  return (
    <div className="gy-post-card" onClick={() => onClick(id)}>
      <article>
        {/* Thumbnail - GreatYOP style */}
        <div className="gyp-article-thumb">
          <a href="#" onClick={(e) => { e.preventDefault(); onClick(id); }}>
            {imageState === ImageLoadState.LOADING && (
              <div className="w-full h-full bg-gray-200 animate-pulse flex items-center justify-center">
                <div className="text-gray-400 text-sm">Chargement...</div>
              </div>
            )}
            <img
              src={imageUrl}
              alt={`${title} - Scholarship thumbnail`}
              className={`${imageState === ImageLoadState.LOADING ? 'opacity-0' : 'opacity-100'}`}
              loading="lazy"
              onLoad={() => setImageState(ImageLoadState.LOADED)}
              onError={(e) => handleImageError(e, constructImageUrl(null))}
            />
          </a>
        </div>

        {/* Content area - GreatYOP style */}
        <div className="gyp-archive-post-header-wrapper">
          <div className="entry-header">
            <h2 className="entry-title">
              <a href="#" onClick={(e) => { e.preventDefault(); onClick(id); }} rel="bookmark">
                {title}
              </a>
            </h2>
          </div>

          {/* Meta information - GreatYOP style */}
          <div className="entry-meta">
            {country && (
              <span className="country-meta">
                <i className="fa fa-globe" aria-hidden="true"></i>
                {country}
              </span>
            )}
            {level && (
              <span className="level-meta">
                <i className="fa fa-bookmark" aria-hidden="true"></i>
                {level}
              </span>
            )}
          </div>
        </div>

        {/* Bottom section - GreatYOP style */}
        <div className="gy-pcard-bottom">
          <p>
            <i className="fa fa-hourglass-half" aria-hidden="true"></i>
            &nbsp;&nbsp;{formattedText}
          </p>
        </div>
      </article>
    </div>
  );
};

export default ScholarshipCard;