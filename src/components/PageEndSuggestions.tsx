import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';
import { constructImageUrl } from '../utils/imageUtils';
import EnhancedScholarshipCard from './EnhancedScholarshipCard';
import EnhancedOpportunityCard from './EnhancedOpportunityCard';

interface SuggestionItem {
  id: number;
  title: string;
  thumbnail?: string;
  deadline?: string;
  isOpen?: boolean;
  isActive?: boolean;
  country?: string;
  level?: string;
  type?: string;
  description?: string;
  slug?: string;
  organization?: string;
  location?: string;
  isRemote?: boolean;
}

interface PageEndSuggestionsProps {
  currentPageType: 'scholarship' | 'country' | 'level' | 'opportunity';
  currentItem?: string;
  excludeId?: number;
  className?: string;
}

const PageEndSuggestions: React.FC<PageEndSuggestionsProps> = ({
  currentPageType,
  currentItem,
  excludeId,
  className = ''
}) => {
  const { translations } = useLanguage();
  const [suggestions, setSuggestions] = useState<SuggestionItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSuggestions();
  }, [currentPageType, currentItem, excludeId]);

  const fetchSuggestions = async () => {
    try {
      setLoading(true);
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';

      let endpoint = '';
      const params = new URLSearchParams();
      params.append('limit', '6'); // 3x2 layout = 6 items

      if (excludeId) {
        params.append('excludeId', excludeId.toString());
      }

      switch (currentPageType) {
        case 'scholarship':
          endpoint = `${apiUrl}/api/scholarships/latest`;
          break;
        case 'country':
          endpoint = `${apiUrl}/api/scholarships/latest`;
          if (currentItem) {
            params.append('excludeCountry', currentItem);
          }
          break;
        case 'level':
          endpoint = `${apiUrl}/api/scholarships/latest`;
          if (currentItem) {
            params.append('excludeLevel', currentItem);
          }
          break;
        case 'opportunity':
          endpoint = `${apiUrl}/api/opportunities/latest`;
          break;
        default:
          endpoint = `${apiUrl}/api/scholarships/latest`;
      }

      const response = await fetch(`${endpoint}?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setSuggestions(data.data || data.scholarships || []);
      }
    } catch (error) {
      console.error('Error fetching suggestions:', error);
    } finally {
      setLoading(false);
    }
  };

  const getItemLink = (item: SuggestionItem) => {
    switch (currentPageType) {
      case 'opportunity':
        return `/opportunities/${item.id}`;
      default:
        if (item.slug) {
          return `/bourse/${item.slug}`;
        }
        return `/scholarships/${item.id}`;
    }
  };

  const getSectionTitle = () => {
    switch (currentPageType) {
      case 'scholarship':
        return 'Bourses Recommandées';
      case 'country':
        return 'Bourses Recommandées';
      case 'level':
        return 'Bourses Recommandées';
      case 'opportunity':
        return 'Opportunités Recommandées';
      default:
        return 'Recommandations';
    }
  };

  if (loading || suggestions.length === 0) {
    return null;
  }

  return (
    <div className={`page-end-suggestions ${className}`} style={{ paddingTop: '16px', paddingBottom: '16px' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-3">
          <h2 className="text-2xl font-bold text-gray-900">
            {getSectionTitle()}
          </h2>
        </div>

        {/* GreatYOP Card Layout - No background frame - Fixed for desktop */}
        <div className="gy-pcard-wrap">
          {suggestions.slice(0, 6).map((item, index) => (
            currentPageType === 'opportunity' ? (
              <EnhancedOpportunityCard
                key={item.id}
                id={item.id}
                title={item.title}
                thumbnail={item.thumbnail}
                deadline={item.deadline || ''}
                isActive={item.isActive !== undefined ? item.isActive : true}
                type={item.type as 'internship' | 'training' | 'conference' | 'workshop' | 'competition'}
                organization={item.organization || ''}
                location={item.location || ''}
                isRemote={item.isRemote || false}
                onClick={(id) => {
                  const link = getItemLink(item);
                  window.location.href = link;
                }}
                index={index}
                variant="greatyop"
              />
            ) : (
              <EnhancedScholarshipCard
                key={item.id}
                id={item.id}
                title={item.title}
                thumbnail={item.thumbnail || ''}
                deadline={item.deadline || ''}
                isOpen={item.isOpen !== undefined ? item.isOpen : true}
                level={item.level}
                country={item.country}
                onClick={(id, slug) => {
                  const link = getItemLink(item);
                  window.location.href = link;
                }}
                index={index}
                variant="greatyop"
              />
            )
          ))}
        </div>
      </div>

      <style>{`
        .page-end-suggestions .gyp-article-thumb {
          height: 50% !important;
        }

        .page-end-suggestions .gyp-archive-post-header-wrapper {
          padding: 0.5rem 1.25rem 0.25rem !important;
        }

        .page-end-suggestions .entry-title {
          margin: 0 0 2px !important;
          font-size: 18px !important;
          line-height: 1.2 !important;
        }

        .page-end-suggestions .gy-pcard-bottom {
          min-height: 80px !important;
          padding: 1.25rem 1.25rem 1.5rem !important;
        }

        @media (min-width: 640px) {
          .page-end-suggestions .gyp-article-thumb {
            height: 50% !important;
          }
        }

        @media (min-width: 1024px) {
          .page-end-suggestions .gyp-article-thumb {
            height: 50% !important;
          }
        }
      `}</style>
    </div>
  );
};

export default PageEndSuggestions;
