import React from 'react';
import { Link } from 'react-router-dom';

interface HeroSectionProps {
  scrollToLatestScholarships: () => void;
}

const HeroSection: React.FC<HeroSectionProps> = ({ scrollToLatestScholarships }) => {
  return (
    <section className="relative bg-gradient-to-r from-gray-900 to-gray-800 overflow-hidden">
      {/* Background image with overlay */}
      <div className="absolute inset-0 z-0">
        <img
          src="/assets/hero-background.jpg"
          alt="Students studying"
          className="w-full h-full object-cover opacity-50"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80';
          }}
        />
        <div className="absolute inset-0 bg-gradient-to-r from-primary/60 to-primary-dark/60 mix-blend-multiply" />
      </div>

      {/* Hero content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-20 lg:py-24">
        <div className="text-center max-w-3xl mx-auto">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white leading-tight">
            Trouvez des Bourses d'Études à Travers le Monde
          </h1>
          
          <p className="mt-6 text-xl text-white/90 leading-relaxed">
            Accédez à des bourses de Licence, Master et Doctorat – totalement ou partiellement financées
          </p>
          
          <div className="mt-8 flex flex-col sm:flex-row justify-center gap-4">
            <button
              onClick={scrollToLatestScholarships}
              className="px-6 py-3 bg-white text-primary font-medium rounded-lg shadow-lg hover:bg-gray-100 transition-all duration-300 flex items-center justify-center"
            >
              Explorer les Bourses
              <svg className="ml-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </button>
            
            <Link
              to="/scholarships"
              className="px-6 py-3 bg-transparent border border-white text-white font-medium rounded-lg hover:bg-white/10 transition-all duration-300 flex items-center justify-center"
            >
              Voir Toutes les Bourses
              <svg className="ml-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
