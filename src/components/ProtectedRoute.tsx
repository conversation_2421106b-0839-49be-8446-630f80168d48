import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin } from 'antd';
import { useAuth } from '../contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireMainAdmin?: boolean;
}

/**
 * Protected route component for admin authentication
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireMainAdmin = false
}) => {
  const { isAuthenticated, isLoading, admin } = useAuth();
  const location = useLocation();
  const [hasCheckedAuth, setHasCheckedAuth] = useState(false);

  // Track when initial auth check is complete
  useEffect(() => {
    if (!isLoading) {
      setHasCheckedAuth(true);
    }
  }, [isLoading]);

  // Show loading spinner while checking authentication
  if (isLoading || !hasCheckedAuth) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Spin size="large" tip="Verifying authentication..." />
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    // Prevent redirect loops by checking current path
    if (location.pathname !== '/admin/login') {
      return <Navigate to="/admin/login" replace />;
    }
    return null;
  }

  // Check main admin requirement
  if (requireMainAdmin && !admin?.isMainAdmin) {
    // Prevent redirect loops by checking current path
    if (location.pathname !== '/admin/dashboard') {
      return <Navigate to="/admin/dashboard" replace />;
    }
    return null;
  }

  // Render protected content
  return <>{children}</>;
};

export default ProtectedRoute;
