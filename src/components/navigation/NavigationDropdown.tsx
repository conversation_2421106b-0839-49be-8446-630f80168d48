/**
 * Navigation Dropdown Component
 * 
 * Specialized dropdown for navigation menus with data fetching,
 * proper routing, and professional styling.
 */

import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { ChevronDown } from 'lucide-react';
import Dropdown, { DropdownItem } from '../common/Dropdown';
import { useLanguage } from '../../context/LanguageContext';

interface NavigationDropdownProps {
  type: 'countries' | 'scholarships' | 'opportunities';
  label: string;
  className?: string;
}

interface CountryData {
  country: string;
  count: number;
}

interface LevelData {
  name: string;
  count: number;
  openCount: number;
  slug: string;
}

interface OpportunityTypeData {
  name: string;
  count: number;
  activeCount: number;
  slug: string;
}

const NavigationDropdown: React.FC<NavigationDropdownProps> = ({
  type,
  label,
  className = ''
}) => {
  const [items, setItems] = useState<DropdownItem[]>([]);
  const [loading, setLoading] = useState(false);
  const { translations } = useLanguage();

  // Fetch data when dropdown opens
  const fetchData = async () => {
    if (items.length > 0) return; // Don't refetch if we already have data
    
    setLoading(true);
    try {
      let endpoint = '';
      let dataProcessor: (data: any[]) => DropdownItem[] = () => [];

      switch (type) {
        case 'countries':
          endpoint = '/api/countries';
          dataProcessor = (countries: CountryData[]) => [
            {
              id: 'all-countries',
              label: translations.navigation.allCountries || 'All Countries',
              href: '/pays',
              count: countries.reduce((sum, c) => sum + c.count, 0)
            },
            ...countries.slice(0, 8).map(country => ({
              id: country.country.toLowerCase().replace(/\s+/g, '-'),
              label: country.country,
              href: `/pays/${encodeURIComponent(country.country)}`,
              count: country.count
            })),
            ...(countries.length > 8 ? [{
              id: 'view-all-countries',
              label: translations.navigation.viewAll || 'View All',
              href: '/pays'
            }] : [])
          ];
          break;

        case 'scholarships':
          endpoint = '/api/scholarships/levels';
          dataProcessor = (levels: LevelData[]) => {
            // Function to map level names to correct routes
            const getLevelRoute = (levelName: string): string => {
              const name = levelName.toLowerCase();
              if (name.includes('licence') || name.includes('undergraduate') || name.includes('bachelor')) {
                return '/licence';
              } else if (name.includes('master')) {
                return '/master';
              } else if (name.includes('doctorat') || name.includes('doctorate') || name.includes('phd')) {
                return '/doctorat';
              } else if (name.includes('graduate')) {
                return '/master'; // Graduate typically refers to Master's level
              }
              // Fallback to the original logic for unknown levels
              return `/${levelName.toLowerCase().replace(/\s+/g, '-')}`;
            };

            // Sort levels in French order: Licence, Master, Doctorat
            const sortLevels = (levels: LevelData[]): LevelData[] => {
              const order = ['licence', 'undergraduate', 'bachelor', 'master', 'graduate', 'doctorat', 'doctorate', 'phd'];
              return levels.sort((a, b) => {
                const aIndex = order.findIndex(term => a.name.toLowerCase().includes(term));
                const bIndex = order.findIndex(term => b.name.toLowerCase().includes(term));
                return (aIndex === -1 ? 999 : aIndex) - (bIndex === -1 ? 999 : bIndex);
              });
            };

            const sortedLevels = sortLevels(levels);
            return [
              {
                id: 'all-scholarships',
                label: translations.navigation.allScholarships || 'All Scholarships',
                href: '/bourses',
                count: levels.reduce((sum, l) => sum + l.count, 0)
              },
              ...sortedLevels.map(level => ({
                id: level.slug,
                label: level.name,
                href: getLevelRoute(level.name),
                count: level.openCount
              }))
            ];
          };
          break;

        case 'opportunities':
          endpoint = '/api/opportunities/types';
          dataProcessor = (types: OpportunityTypeData[]) => {
            const typeRoutes = {
              'training': '/formation',
              'internship': '/stage',
              'conference': '/conference',
              'workshop': '/atelier',
              'competition': '/concours'
            };

            return [
              {
                id: 'all-opportunities',
                label: translations.navigation.allOpportunities || 'All Opportunities',
                href: '/opportunities',
                count: types.reduce((sum, t) => sum + t.count, 0)
              },
              ...types.map(opType => ({
                id: opType.slug,
                label: opType.name.charAt(0).toUpperCase() + opType.name.slice(1),
                href: typeRoutes[opType.name as keyof typeof typeRoutes] || `/opportunities/type/${encodeURIComponent(opType.name)}`,
                count: opType.activeCount
              }))
            ];
          };
          break;
      }

      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${endpoint}`);
      if (!response.ok) throw new Error('Failed to fetch data');
      
      const result = await response.json();
      const data = result.data || result;
      
      setItems(dataProcessor(data));
    } catch (error) {
      console.error(`Error fetching ${type} data:`, error);
      setItems([{
        id: 'error',
        label: 'Failed to load data',
        disabled: true
      }]);
    } finally {
      setLoading(false);
    }
  };

  // Get the main page URL for this navigation type
  const getMainPageUrl = () => {
    switch (type) {
      case 'countries':
        return '/pays';
      case 'scholarships':
        return '/bourses';
      case 'opportunities':
        return '/opportunities';
      default:
        return '/';
    }
  };

  const trigger = (
    <div className={`
      flex items-center rounded-lg text-sm font-medium
      transition-all duration-200 ease-in-out
      ${className?.includes('professional-nav-item')
        ? 'px-4 py-2 text-gray-700 hover:text-primary hover:bg-white/70 hover:shadow-sm'
        : 'px-3 py-2 text-gray-700 hover:text-primary hover:bg-primary/5'
      }
      ${className || ''}
    `}>
      {/* Main clickable link */}
      <Link
        to={getMainPageUrl()}
        className="transition-colors duration-200"
        onClick={(e) => e.stopPropagation()} // Prevent dropdown from opening when clicking the link
      >
        <span className="transition-colors duration-200">{label}</span>
      </Link>

      {/* Dropdown arrow - separate clickable area */}
      <div
        className="ml-1 text-gray-400 hover:text-primary transition-colors duration-200 cursor-pointer"
        onClick={(e) => {
          e.stopPropagation();
          // This will be handled by the dropdown's click handler
        }}
      >
        <ChevronDown
          size={14}
          className="transition-all duration-200 ease-out group-hover:rotate-180"
        />
      </div>
    </div>
  );

  return (
    <div className="group relative">
      <Dropdown
        trigger={trigger}
        items={items}
        loading={loading}
        onOpen={fetchData}
        showOnHover={true}
        closeOnClick={true}
        placement="bottom-left"
        className=""
        dropdownClassName=""
        emptyMessage={`No ${type} available`}
      />
    </div>
  );
};

export default NavigationDropdown;
