import React from 'react';
import { Link, useLocation, Outlet, useNavigate } from 'react-router-dom';
import { SettingOutlined, LogoutOutlined, SecurityScanOutlined, AreaChartOutlined, MailOutlined } from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';
import { Dropdown, Menu, Avatar } from 'antd';

const AdminLayout: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { admin, logout } = useAuth();
  const isMainAdmin = admin?.isMainAdmin || false;

  // Define menu items based on admin role
  const menuItems = [
    // Common items for all admins
    { path: '/admin/dashboard', label: 'Dashboard', icon: '📊' },
    { path: '/admin/scholarships', label: 'Scholarships', icon: '🎓' },
    { path: '/admin/guides', label: 'Guides', icon: '📚' },
    { path: '/admin/opportunities', label: 'Opportunities', icon: '🚀' },
    { path: '/admin/messages', label: 'Messages', icon: '✉️' },
    { path: '/admin/contact-management', label: 'Contact Management', icon: '📞' },
    { path: '/admin/newsletter', label: 'Newsletter', icon: '📧' },

    // Analytics available to all admins
    { path: '/admin/analytics', label: 'Analytics', icon: <AreaChartOutlined /> },

    // Email notifications available to all admins
    { path: '/admin/email-notifications', label: 'Email Notifications', icon: <MailOutlined /> },

    // Admin Management only for main admin
    ...(isMainAdmin ? [
      { path: '/admin/admins', label: 'Admin Management', icon: '👥' },
      { path: '/admin/security-dashboard', label: 'Security Dashboard', icon: '🔒' }
    ] : []),

    // Settings available to all admins (but with restricted options for non-main)
    { path: '/admin/settings', label: 'Settings', icon: <SettingOutlined /> },

    // Security available to all admins (for changing own password)
    { path: '/admin/security', label: 'Security', icon: <SecurityScanOutlined /> },
  ];

  const handleLogout = async () => {
    try {
      await logout();
      // Add a small delay to ensure state is cleared before navigation
      setTimeout(() => {
        navigate('/admin/login', { replace: true });
      }, 100);
    } catch (error) {
      console.error('Logout error:', error);
      // Still redirect to login page even if logout fails
      setTimeout(() => {
        navigate('/admin/login', { replace: true });
      }, 100);
    }
  };

  const userMenu = (
    <Menu>
      <Menu.Item key="profile" icon={<SettingOutlined />}>
        Profile
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="logout" icon={<LogoutOutlined />} onClick={handleLogout}>
        Logout
      </Menu.Item>
    </Menu>
  );

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className="w-64 bg-gradient-to-b from-blue-50 via-indigo-50 to-purple-50 shadow-lg relative">
        {/* Decorative elements */}
        <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiBwYXR0ZXJuVW5pdHM9InVzZXJTcGFjZU9uVXNlIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSg0NSkiPjxsaW5lIHgxPSIwIiB5PSIwIiB4Mj0iMCIgeTI9IjQwIiBzdHJva2U9InJnYmEoMCwwLDAsMC4wNSkiIHN0cm9rZS13aWR0aD0iMSIvPjwvcGF0dGVybj48L2RlZnM+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNwYXR0ZXJuKSIvPjwvc3ZnPg==')] opacity-20"></div>

        <div className="p-4 relative">
          <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Admin Dashboard</h1>
        </div>

        <nav className="mt-6 relative">
          {menuItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              onClick={(e) => {
                // Prevent default if we're already on this page
                if (location.pathname === item.path) {
                  e.preventDefault();
                }
              }}
              className={`flex items-center px-4 py-3 text-gray-700 hover:bg-white/50 hover:text-blue-600 transition-all duration-300 ${
                location.pathname === item.path
                  ? 'bg-white/80 text-blue-600 border-r-4 border-blue-500 shadow-sm'
                  : ''
              }`}
            >
              <span className="mr-3 text-lg">{item.icon}</span>
              <span className="font-medium">{item.label}</span>
            </Link>
          ))}
        </nav>

        {/* User Profile Section */}
        <div className="absolute bottom-0 w-full p-4 border-t border-gray-200">
          <Dropdown overlay={userMenu} placement="topRight">
            <div className="flex items-center space-x-3 p-2 hover:bg-white/50 rounded-lg cursor-pointer">
              <Avatar
                size="large"
                className="bg-blue-500 flex items-center justify-center"
                style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              >
                <span style={{ fontSize: '16px', lineHeight: '1' }}>
                  {admin?.name?.[0]?.toUpperCase() || 'A'}
                </span>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {admin?.name || 'Admin'}
                </p>
                <p className="text-xs text-gray-500 truncate">
                  {admin?.role === 'super_admin' ? 'Main Admin' : admin?.role || 'Admin'}
                </p>
              </div>
            </div>
          </Dropdown>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <div className="p-8">
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default AdminLayout;